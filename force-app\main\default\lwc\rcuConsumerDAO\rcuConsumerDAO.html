<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 05-19-2025
  @last modified by  : <EMAIL>
-->
<template>
    <div class="rcu-app-container rcuHeader-content-adjustment">
        <!-- Session timeout warning banner -->
        <!-- <template if:true={showIdleWarning}>
            <div class="rcu-timeout-warning-banner">
                <div class="rcu-warning-content">
                    <span>Your application will time out in 2 minutes. Need more time?</span>
                    <div class="rcu-warning-actions">
                        <a href="javascript:void(0);" onclick={handleKeepMeHere}>Yes, keep me here</a>
                        <span class="rcu-separator">|</span>
                        <a href="javascript:void(0);" onclick={handleImDone}>No, I'm done</a>
                    </div>
                </div>
            </div>
        </template> -->

        <!-- Background images -->
        <div class="rcu-right-background-image"></div>
        <div class="rcu-left-background-image"></div>
       
        <c-rcu-consumer-d-a-o-header
            current-screen={currentScreenNumber}
            onscreennavigate={handleScreenNavigation}>
        </c-rcu-consumer-d-a-o-header>
       
        <template if:true={isWelcomePage}>
            <c-cdao-welcome form-data={applicationData.welcomeInfo} onnext={handleNext}></c-cdao-welcome>
        </template>
  
        <template if:true={isQuestionScreen}>
            <c-cdao-new-app form-data={applicationData.questionInfo} onnext={handleNext} onback={handleBack}></c-cdao-new-app>
        </template>
  
        <template if:true={isPrimaryMemberInfoScreen}>
            <c-cdao-member-information 
                application-data={getPrimaryMemberData} 
                onnext={handleNext} 
                onback={handleBack}
                onsaveinfo={handleSaveInfo}>
            </c-cdao-member-information>
        </template>

        <template if:true={isAddressAndIdScreen}>
            <c-cdao-address-and-id-information 
                application-data={applicationData.addressAndIdInfo} 
                onnext={handleNext} 
                onback={handleBack}
                onaddapplicant={handleAddApplicant}>
            </c-cdao-address-and-id-information>
        </template>

        <c-rcu-consumer-d-a-o-footer></c-rcu-consumer-d-a-o-footer>
        
        <!-- Idle timeout modal -->
        <!-- <template if:true={showIdleModal}>
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="rcu-modal-container slds-modal__container">
                    <header class="slds-modal__header">
                        <h2 id="modal-heading-01" class="slds-modal__title slds-hyphenate">Session Timeout Warning</h2>
                    </header>
                    <div class="rcu-modal-content slds-modal__content slds-p-around_medium" id="modal-content-id-1">
                        <p>You have been idle for 2 minutes. Your session may timeout soon. Please continue with your application to prevent data loss.</p>
                    </div>
                    <footer class="slds-modal__footer">
                        <button class="slds-button slds-button_brand" onclick={handleCloseIdleModal}>Continue</button>
                    </footer>
                </div>
            </section>
            <div class="rcu-backdrop slds-backdrop slds-backdrop_open"></div>
        </template> -->
    </div>
  </template>






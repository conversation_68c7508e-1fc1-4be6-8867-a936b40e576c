import { createElement } from '@lwc/engine-dom';
import RcuConsumerDAOHeader from 'c/rcuConsumerDAOHeader';

describe('c-rcu-consumer-dao-header', () => {
    afterEach(() => {
        // The jsdom instance is shared across test cases in a single file so reset the DOM
        while (document.body.firstChild) {
            document.body.removeChild(document.body.firstChild);
        }
    });

    it('TODO: test case generated by CLI command, please fill in test logic', () => {
        // Arrange
        const element = createElement('c-rcu-consumer-dao-header', {
            is: RcuConsumerDAOHeader
        });

        // Act
        document.body.appendChild(element);

        // Assert
        // const div = element.shadowRoot.querySelector('div');
        expect(1).toBe(1);
    });
});
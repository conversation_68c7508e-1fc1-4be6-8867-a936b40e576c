import { LightningElement, api, wire, track } from 'lwc';
import { getPicklistValues } from 'lightning/uiObjectInfoApi';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import DAO_ROLES_OBJECT from '@salesforce/schema/DAO_Roles__c';
import STATE_FIELD from '@salesforce/schema/DAO_Roles__c.State__c';
import getAddress from '@salesforce/apex/cdaoAddressAutoComplete.getAddress';

export default class CdaoAddressComponent extends LightningElement {
    // Public properties
    @api street = '';
    @api city = '';
    @api state = '';
    @api zipCode = '';
    @api required = false;
    @api addressType = ''; // Physical, Mailing, Business, etc.
    @api cardTitle = 'Address Information';
    
    // Custom labels
    @api streetLabel = 'Street Address';
    @api cityLabel = 'City';
    @api stateLabel = 'State';
    @api zipLabel = 'ZIP Code';
    
    // Track error state
    @track zipCodeError = '';
    
    // Address auto-complete properties
    @track addressSuggestions = [];
    @track showSuggestions = false;
    @track isSearching = false;
    doneTypingInterval = 300;
    typingTimer;
    
    // Track the complete address object - without addressType
    @track addressObject = {
        street: '',
        city: '',
        state: '',
        zipCode: '',
        isValid: true
    };
    
    // Get object info for picklist values
    @wire(getObjectInfo, { objectApiName: DAO_ROLES_OBJECT })
    objectInfo;
    
    // Get state picklist values
    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: STATE_FIELD })
    wiredStateOptions({ error, data }) {
        if (data) {
            this.stateOptions = data.values;
        } else if (error) {
            console.error('Error loading state options', error);
        }
    }
    
    stateOptions = [];
    
    // Initialize the component
    connectedCallback() {
        console.log('CdaoAddressComponent initialized');
        this.initializeAddressObject();
    }
    
    // Initialize the address object with any provided values
    initializeAddressObject() {
        this.addressObject = {
            street: this.street,
            city: this.city,
            state: this.state,
            zipCode: this.zipCode,
            isValid: this.isValid()
        };
    }
    
    // Handle input changes
    handleInputChange(event) {
        const field = event.target.name;
        const value = event.target.value;
        
        console.log(`Field ${field} changed to: ${value}`);
        
        // Update the component property
        this[field] = value;
        
        // Update the address object
        this.addressObject[field] = value;
        
        // If this is the street field, trigger address search
        if (field === 'street' && value.length > 3) {
            // Clear previous timer
            clearTimeout(this.typingTimer);
            
            // Set searching state
            this.isSearching = true;
            
            // Set a new timer for debounce
            this.typingTimer = setTimeout(() => {
                console.log('Searching for address:', value);
                this.fetchAddressSuggestions(value);
            }, this.doneTypingInterval);
        } else if (field === 'street') {
            // Clear suggestions if street field is too short
            this.addressSuggestions = [];
            this.showSuggestions = false;
            clearTimeout(this.typingTimer);
            this.isSearching = false;
        }
        
        // Clear ZIP code error when user is typing
        if (field === 'zipCode') {
            this.validateZipCode(value);
        }
        
        // Update validity
        this.addressObject.isValid = this.isValid();
        
        // Dispatch change event with all address data
        this.dispatchAddressChange();
    }
    
    // Handle keyup events for immediate feedback
    handleKeyUp(event) {
        // Only process if it's the street field
        if (event.target.name === 'street') {
            const value = event.target.value;
            
            // If the value is long enough, trigger the search
            if (value && value.length > 3) {
                // Clear previous timer
                clearTimeout(this.typingTimer);
                
                // Set searching state
                this.isSearching = true;
                
                // Set a new timer for debounce
                this.typingTimer = setTimeout(() => {
                    console.log('Searching for address:', value);
                    this.fetchAddressSuggestions(value);
                }, this.doneTypingInterval);
            } else {
                // Clear suggestions if street field is too short
                this.addressSuggestions = [];
                this.showSuggestions = false;
                clearTimeout(this.typingTimer);
                this.isSearching = false;
            }
        }
    }
    
    // Fetch address suggestions from the server
    fetchAddressSuggestions(searchText) {
        if (!searchText || searchText.length < 3) {
            this.addressSuggestions = [];
            this.showSuggestions = false;
            return;
        }
        
        this.isSearching = true;
        console.log('Fetching address suggestions for:', searchText);
        
        getAddress({ search: searchText })
            .then(result => {
                console.log('Raw API response:', result);
                try {
                    // Parse the response if it's a string
                    let parsedResult;
                    if (typeof result === 'string') {
                        parsedResult = JSON.parse(result);
                    } else {
                        parsedResult = result;
                    }
                    
                    let suggestions = [];
                    
                    // Check if the response has a suggestions array
                    if (parsedResult && parsedResult.suggestions && Array.isArray(parsedResult.suggestions)) {
                        console.log('Found suggestions array with length:', parsedResult.suggestions.length);
                        
                        parsedResult.suggestions.forEach(elem => {
                            const fullAddress = elem.street_line + 
                                              (elem.secondary ? ' ' + elem.secondary : '') + 
                                              ', ' + elem.city + 
                                              ', ' + elem.state + 
                                              ' ' + elem.zipcode;
                            
                            console.log('Creating suggestion with value:', fullAddress);
                            
                            suggestions.push({
                                id: '' + Date.now() + Math.random(), // Convert to string
                                value: fullAddress,
                                street: elem.street_line + (elem.secondary ? ' ' + elem.secondary : ''),
                                city: elem.city,
                                state: elem.state,
                                zipCode: elem.zipcode
                            });
                        });
                        
                        this.addressSuggestions = [...suggestions]; // Create a new array to trigger reactivity
                        this.showSuggestions = suggestions.length > 0;
                        
                        // Force a re-render
                        this.template.querySelector('.address-field').classList.add('has-suggestions');
                        
                        console.log('Final suggestions count:', this.addressSuggestions.length);
                        console.log('First suggestion value:', this.addressSuggestions.length > 0 ? this.addressSuggestions[0].value : 'none');
                        console.log('showSuggestions set to:', this.showSuggestions);
                    } else {
                        this.addressSuggestions = [];
                        this.showSuggestions = false;
                        console.log('No suggestions found or invalid format');
                    }
                    this.isSearching = false;
                } catch (error) {
                    console.error('Error parsing address suggestions:', error);
                    this.addressSuggestions = [];
                    this.showSuggestions = false;
                    this.isSearching = false;
                }
            })
            .catch(error => {
                console.error('Error fetching address suggestions:', error);
                this.isSearching = false;
                this.addressSuggestions = [];
                this.showSuggestions = false;
            });
    }
    
    // Handle selection of an address suggestion
    handleAddressSelection(event) {
        const selectedId = event.currentTarget.dataset.id;
        console.log('Selected suggestion ID:', selectedId);
        
        const selectedAddress = this.addressSuggestions.find(
            suggestion => suggestion.id == selectedId
        );
        
        console.log('Selected address:', selectedAddress);
        
        if (selectedAddress) {
            // Update component properties
            this.street = selectedAddress.street;
            this.city = selectedAddress.city;
            this.state = selectedAddress.state;
            this.zipCode = selectedAddress.zipCode;
            
            // Update address object
            this.addressObject = {
                street: this.street,
                city: this.city,
                state: this.state,
                zipCode: this.zipCode,
                isValid: true
            };
            
            // Clear suggestions
            this.addressSuggestions = [];
            this.showSuggestions = false;
            
            // Validate ZIP code
            this.validateZipCode(this.zipCode);
            
            // Dispatch change event
            this.dispatchAddressChange();
            
            console.log('Address updated with selection');
        }
    }
    
    // Close suggestions when clicking outside
    handleBlur() {
        console.log('Blur event triggered');
        // Use setTimeout to allow click events to fire before closing dropdown
        setTimeout(() => {
            this.showSuggestions = false;
            console.log('Suggestions hidden after blur');
        }, 300);
    }
    
    // Validate ZIP code
    validateZipCode(value) {
        const zipRegex = /^\d{5}$/;
        
        if (value && !zipRegex.test(value)) {
            this.zipCodeError = 'Please enter a valid 5-digit ZIP code';
            return false;
        } else {
            this.zipCodeError = '';
            return true;
        }
    }
    
    // Dispatch address change event
    dispatchAddressChange() {
        // Include addressType in the event detail, but not in the addressObject
        this.dispatchEvent(new CustomEvent('addresschange', {
            detail: {
                ...this.addressObject,
                addressType: this.addressType
            },
            bubbles: true,
            composed: true
        }));
    }
    
    // Check if address is valid
    isValid() {
        // If not required and all fields are empty, consider it valid
        if (!this.required && 
            !this.street && 
            !this.city && 
            !this.state && 
            !this.zipCode) {
            return true;
        }
        
        // If required or any field has a value, validate all fields
        const isZipValid = !this.zipCode || this.validateZipCode(this.zipCode);
        const allFieldsPresent = this.street && this.city && this.state && this.zipCode;
        
        return isZipValid && (this.required ? allFieldsPresent : true);
    }
    
    // Public method to validate the address
    @api
    validate() {
        // Validate all fields
        const isValid = this.isValid();
        
        // If required and any field is missing, mark as invalid
        if (this.required && (!this.street || !this.city || !this.state || !this.zipCode)) {
            return {
                isValid: false,
                errorMessage: 'Please complete all required address fields'
            };
        }
        
        // If ZIP code is invalid, mark as invalid
        if (this.zipCode && !this.validateZipCode(this.zipCode)) {
            return {
                isValid: false,
                errorMessage: 'Please enter a valid 5-digit ZIP code'
            };
        }
        
        return { isValid: true };
    }
    
    // Reset the component
    @api
    reset() {
        this.street = '';
        this.city = '';
        this.state = '';
        this.zipCode = '';
        this.zipCodeError = '';
        
        // Reset the address object
        this.initializeAddressObject();
    }
    
    // Get the complete address object
    @api
    getAddressObject() {
        // Return address object with addressType as a separate property
        return {
            ...this.addressObject,
            addressType: this.addressType
        };
    }
    
    // Set the address object from parent
    @api
    setAddressObject(addressData) {
        if (addressData) {
            this.street = addressData.street || '';
            this.city = addressData.city || '';
            this.state = addressData.state || '';
            this.zipCode = addressData.zipCode || '';
            
            // Set addressType if provided, but keep it separate from addressObject
            if (addressData.addressType) {
                this.addressType = addressData.addressType;
            }
            
            // Update the address object (without addressType)
            this.initializeAddressObject();
        }
    }

    // Test method to verify API connectivity
    @api
    testAddressSearch(testValue) {
        console.log('Testing address search with:', testValue);
        this.isSearching = true;
        
        getAddress({ search: testValue })
            .then(result => {
                console.log('Test search result:', result);
                this.isSearching = false;
            })
            .catch(error => {
                console.error('Test search error:', error);
                this.isSearching = false;
            });
    }
}










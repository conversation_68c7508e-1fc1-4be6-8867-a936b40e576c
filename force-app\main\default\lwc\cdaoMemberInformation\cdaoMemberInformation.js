import { LightningElement, api, track, wire } from 'lwc';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import { getPicklistValues } from 'lightning/uiObjectInfoApi';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import ROLES_OBJECT from '@salesforce/schema/DAO_Roles__c';
import Suffix_Options from '@salesforce/schema/DAO_Roles__c.Suffix__c';
import { loadStyle } from 'lightning/platformResourceLoader';
import ValidationHelper from './memberInfoValidationHelper';
import { FIELD_DEFINITIONS, HEADINGS, EVENTS} from './memberInfoConstants';

export default class CdaoMemberInformation extends LightningElement {
    @api applicationData;
    suffixOptions = [];
    
    // Initialize fields with definitions from constants
    @track fields = {};
    
    isSSNFocused = false;
    
    currentMemberTypeHeading = HEADINGS.PRIMARY_MEMBER_TYPE;
    currentMemberInfoHeading = HEADINGS.PRIMARY_MEMBER_INFO;
   
    @wire(getObjectInfo, { objectApiName: ROLES_OBJECT })
    objectInfo;
    
    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: Suffix_Options })
    wiredRolesSuffix({ error, data }) {
        if (data) {
            this.suffixOptions = data.values;
        } else if (error) {
            console.error('Error loading suffix options:', error);
        }
    }

    connectedCallback() {
        // Initialize fields from field definitions
        this.initializeFields();
        
        loadStyle(this, SHARED_STYLES)
            .then(() => {})
            .catch(error => {
                console.error('Error loading shared styles:', error);
            });

        if (this.applicationData) {
            this.loadApplicationData();
        }
    }
    
    // Initialize fields from field definitions
    initializeFields() {
        Object.keys(FIELD_DEFINITIONS).forEach(fieldName => {
            const definition = FIELD_DEFINITIONS[fieldName];
            this.fields[fieldName] = { 
                value: definition.defaultValue || '', 
                error: '', 
                required: definition.required, 
                validationFn: definition.validationFn,
                validationArgs: definition.validationArgs
            };
        });
    }
    
    // Load application data into fields
    loadApplicationData() {
        // Check if we have MemberInformation structure
        if (this.applicationData.MemberInformation) {
            // Copy all properties from MemberInformation to fields
            Object.keys(this.applicationData.MemberInformation).forEach(key => {
                if (this.fields[key]) {
                    this.fields[key].value = this.applicationData.MemberInformation[key] || '';
                }
            });
        } else {
            // Backward compatibility - copy from flat structure
            Object.keys(this.applicationData).forEach(key => {
                if (this.fields[key]) {
                    this.fields[key].value = this.applicationData[key] || '';
                }
            });
        }
        
        // Ensure Individual_Role is set
        if (!this.fields.Individual_Role.value) {
            this.fields.Individual_Role.value = 'Primary';
        }
    }

    // Getters for template access
    get First_Name() { return this.fields.First_Name.value; }
    get Middle_Name() { return this.fields.Middle_Name.value; }
    get Last_Name() { return this.fields.Last_Name.value; }
    get Suffix() { return this.fields.Suffix.value; }
    get Date_of_Birth() { return this.fields.Date_of_Birth.value; }
    get SSN_TIN() { return this.fields.SSN_TIN.value; }
    get Email_Address() { return this.fields.Email_Address.value; }
    get Phone() { return this.fields.Phone.value; }
    
    get Date_of_Birth_Error() { return this.fields.Date_of_Birth.error; }
    
    get displaySSN() {
        return this.isSSNFocused ? this.fields.SSN_TIN.value : this.fields.SSN_TIN.value.replace(/./g, '•');
    }

    get isSaveDisabled() {
        // Check form validity without showing validation errors
        return !ValidationHelper.isFormValid(this.fields, false);
    }

    // Validate a single field - now using helper
    validateField(fieldName) {
        const field = this.fields[fieldName];
        if (field.validationFn) {
            const validationFn = ValidationHelper[field.validationFn];
            const args = field.validationArgs || [];
            return ValidationHelper.validateField(field, validationFn, args);
        }
        return true;
    }

    // Validate all fields - now using helper
    validateAllFields() {
        return ValidationHelper.validateAllFields(this.fields);
    }

    // Check form validity - now using helper
    isFormValid() {
        return ValidationHelper.isFormValid(this.fields);
    }

    // Handle input - now using helper
    handleInput(event) {
        ValidationHelper.handleInput(event, this.fields, this);
    }

    // Handle DOB input - now using helper
    handleDOBInput(event) {
        ValidationHelper.handleDOBInput(event, this.fields, this);
    }

    handleSSNFocus() {
        this.isSSNFocused = true;
        const ssnInput = this.template.querySelector('lightning-input[data-id="ssn-input"]');
        if (ssnInput) {
            ssnInput.value = this.fields.SSN_TIN.value;
        }
    }

    handleSSNBlur() {
        this.isSSNFocused = false;
        const ssnInput = this.template.querySelector('lightning-input[data-id="ssn-input"]');
        if (ssnInput) {
            ssnInput.value = this.fields.SSN_TIN.value.replace(/./g, '•');
        }
    }

    handleNext() {
        // Validate all fields when user tries to proceed
        if (ValidationHelper.isFormValid(this.fields, true)) {
            // Create member info object from fields
            const memberInfo = Object.keys(this.fields).reduce((obj, fieldName) => {
                obj[fieldName] = this.fields[fieldName].value;
                return obj;
            }, { Individual_Role: 'Primary' });

            const eventDetails ={
                    nextScreen : 'addressAndIdScreen',
                    screenData:  memberInfo
            };
            
            // Just dispatch the next event with the data
            // The parent component will handle saving
            this.dispatchEvent(new CustomEvent(EVENTS.NEXT, { 
                detail: eventDetails,
                bubbles: true,
                composed: true
            }));
        }
    }

    handleBack() {
        const eventDetails ={
                nextScreen : 'questionScreen'
        };
        this.dispatchEvent(new CustomEvent(EVENTS.BACK,{
            detail: eventDetails
        }));
    }
}





















import { LightningElement, api, track, wire } from 'lwc';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import { getPicklistValues } from 'lightning/uiObjectInfoApi';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import APPLICATION_OBJECT from '@salesforce/schema/DAO_Application__c';
import State_Options from '@salesforce/schema/DAO_Application__c.Physical_State__c';
import ID_Country_FIELD from '@salesforce/schema/DAO_Roles__c.ID_Country__c';
import { loadStyle } from 'lightning/platformResourceLoader';
import AddressIdHelper from './addressIdHelper';
import { ID_TYPE_GROUPS, DEFAULT_APPLICATION_DATA, ERROR_MESSAGES, EVENTS} from './addressIdConstants';

export default class CdaoAddressAndIdInformation extends LightningElement {
    @api 
    get applicationData() {
        return this._applicationData;
    }
    set applicationData(value) {
        // Create a deep clone to avoid proxy issues
        this._applicationData = value ? JSON.parse(JSON.stringify(value)) : DEFAULT_APPLICATION_DATA;
    }
    
    // Private backing field
    _applicationData = DEFAULT_APPLICATION_DATA;
    
    @track showAddressConfirmationModal = false;
    @track showMailingAddress = false; // Track UI state for mailing address visibility
    
    // Track date validation errors
    @track idIssueDateError = '';
    @track idExpirationDateError = '';
    
    // Track which button was clicked
    lastButtonClicked = '';
    
    // State options for dropdown
    stateOptions = [];

    @wire(getObjectInfo, { objectApiName: APPLICATION_OBJECT })
    objectInfo;

    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: State_Options })
    wiredStateOptions({ error, data }) {
        if (data) {
            this.stateOptions = data.values;
        } else if (error) {
            console.error('Error loading state options:', error);
        }
    }

    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: ID_Country_FIELD })
    wiredCountryOptions({ error, data }) {
        if (data) {
            this.countryOptions = data.values;
        } else if (error) {
            console.error('Error loading country options', error);
        }
    }

    // Add property for country options
    countryOptions = [];

    // ID type options
    @track isIdTypeDropdownOpen = false;
    @track selectedIdTypeLabel = '';

    // Add the grouped ID type options from constants
    groupedIdTypeOptions = ID_TYPE_GROUPS;

    // Get flat ID type options using helper
    get idTypeOptions() {
        return AddressIdHelper.getFlatIdTypeOptions(this.groupedIdTypeOptions);
    }

    // Add this getter for the dropdown style
    get idTypeDropdownStyle() {
        return this.isIdTypeDropdownOpen ? 'display: block;' : 'display: none;';
    }

    // Add ZIP code validation
    @track zipCodeError = '';
    @track mailingZipCodeError = '';

    // For UI display - checkbox is inverted from model
    get useDifferentMailingAddress() { 
        return this.showMailingAddress;
    }

    // Getters for template access
    get Physical_Street_Address() { return this._applicationData?.PhysicalAddress?.Physical_Street_Address || ''; }
    get Physical_City() { return this._applicationData?.PhysicalAddress?.Physical_City || ''; }
    get Physical_State() { return this._applicationData?.PhysicalAddress?.Physical_State || ''; }
    get Physical_Zip_code() { return this._applicationData?.PhysicalAddress?.Physical_Zip_code || ''; }
    get Mailing_Address_same_as_Physical() { 
        return this._applicationData?.Mailing_Address_same_as_Physical !== undefined 
            ? this._applicationData.Mailing_Address_same_as_Physical 
            : true; 
    }
    get Mailing_Street_Address() { return this._applicationData?.MailingAddress?.Mailing_Street_Address || ''; }
    get Mailing_City() { return this._applicationData?.MailingAddress?.Mailing_City || ''; }
    get Mailing_State() { return this._applicationData?.MailingAddress?.Mailing_State || ''; }
    get Mailing_Zip_code() { return this._applicationData?.MailingAddress?.Mailing_Zip_code || ''; }
    get Type() { return this._applicationData?.IdentityInfo?.Type || ''; }
    get ID_Number() { return this._applicationData?.IdentityInfo?.ID_Number || ''; }
    get ID_State() { return this._applicationData?.IdentityInfo?.ID_State || ''; }
    get ID_Issued_Date() { return this._applicationData?.IdentityInfo?.ID_Issued_Date || ''; }
    get ID_Expiration_Date() { return this._applicationData?.IdentityInfo?.ID_Expiration_Date || ''; }
    get ID_Country() { return this._applicationData?.IdentityInfo?.ID_Country || 'US'; }

    connectedCallback() {
        // Load shared styles
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                //logic here
            })
            .catch(error => {
                console.error('Error loading shared styles:', error);
            });
        
        // Ensure all required objects exist
        this.ensureDataStructure();
        
        // Initialize UI state based on data model
        this.showMailingAddress = !this._applicationData.Mailing_Address_same_as_Physical;

        // Set the selected ID type label
        this.updateSelectedIdTypeLabel();
        
        // Set initial field visibility based on the current ID type
        if (this._applicationData?.IdentityInfo?.Type) {
            this.updateFieldVisibility(this._applicationData.IdentityInfo.Type);
        }
    }
    
    // Ensure all required objects exist in the data structure
    ensureDataStructure() {
        if (!this._applicationData) {
            this._applicationData = {};
        }
        
        if (!this._applicationData.PhysicalAddress) {
            this._applicationData.PhysicalAddress = {};
        }
        
        if (!this._applicationData.MailingAddress) {
            this._applicationData.MailingAddress = {};
        }
        
        if (!this._applicationData.IdentityInfo) {
            this._applicationData.IdentityInfo = {};
        }
        
        if (this._applicationData.Mailing_Address_same_as_Physical === undefined) {
            this._applicationData.Mailing_Address_same_as_Physical = true;
        }
    }

    handleInput(event) {
        const field = event.target.name;
        const value = event.target.value;
        
        // Ensure data structure exists
        this.ensureDataStructure();
        
        // Determine which section this field belongs to
        if (field.startsWith('Physical_')) {
            this._applicationData.PhysicalAddress[field] = value;
        } else if (field.startsWith('Mailing_')) {
            this._applicationData.MailingAddress[field] = value;
        } else if (field === 'Type' || field.startsWith('ID_')) {
            this._applicationData.IdentityInfo[field] = value;
        }
        
        // Clear any error messages for ZIP codes
        if (field === 'Physical_Zip_code') {
            this.zipCodeError = '';
        } else if (field === 'Mailing_Zip_code') {
            this.mailingZipCodeError = '';
        }
    }
    
    // Handle date input for ID issue date
    handleIDIssueDateInput(event) {
        const value = event.target.value;
        const formattedDate = AddressIdHelper.formatDate(value);
        
        // Update input field
        const dateInput = this.template.querySelector('lightning-input[name="ID_Issued_Date"]');
        if (dateInput) {
            dateInput.value = formattedDate;
        }
        
        // Update field value
        this._applicationData.IdentityInfo.ID_Issued_Date = formattedDate;
        
        // Validate date
        if (formattedDate.length === 10) {
            AddressIdHelper.validateIDIssueDate(formattedDate, this);
        } else if (formattedDate.length > 0) {
            this.idIssueDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
        } else {
            this.idIssueDateError = ERROR_MESSAGES.ID_ISSUE_DATE_REQUIRED;
        }
    }
    
    // Handle date input for ID expiration date
    handleIDExpirationDateInput(event) {
        const value = event.target.value;
        const formattedDate = AddressIdHelper.formatDate(value);
        
        // Update input field
        const dateInput = this.template.querySelector('lightning-input[name="ID_Expiration_Date"]');
        if (dateInput) {
            dateInput.value = formattedDate;
        }
        
        // Update field value
        this._applicationData.IdentityInfo.ID_Expiration_Date = formattedDate;
        
        // Validate date
        if (formattedDate.length === 10) {
            AddressIdHelper.validateIDExpirationDate(
                formattedDate, 
                this._applicationData.IdentityInfo.ID_Issued_Date,
                this
            );
        } else if (formattedDate.length > 0) {
            this.idExpirationDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
        } else {
            this.idExpirationDateError = ERROR_MESSAGES.ID_EXPIRATION_DATE_REQUIRED;
        }
    }

    handleCheckboxChange(event) {
        
        // Update both the data model and UI state
        this._applicationData.Mailing_Address_same_as_Physical = !event.target.checked;
        this.showMailingAddress = event.target.checked;
        
    }
    
    handleRemoveMailingAddress() {
        this._applicationData.Mailing_Address_same_as_Physical = true;
        this.showMailingAddress = false;
    }

    get isSaveDisabled() {
        return !this.isFormValid();
    }

    isFormValid() {
        return AddressIdHelper.validateAllFields(this);
    }

    handleNext() {
        this.lastButtonClicked = 'next';
        if (this.isFormValid()) {
            this.showAddressConfirmationModal = true;
        } else {
            // Show validation errors
            AddressIdHelper.showValidationErrors(this.template);
        }
    }

    handleBack() {
        this.dispatchEvent(new CustomEvent(EVENTS.BACK));
    }
    
    handleAddApplicant() {
        this.lastButtonClicked = 'addapplicant';
        if (this.isFormValid()) {
            this.showAddressConfirmationModal = true;
        } else {
            AddressIdHelper.showValidationErrors(this.template);
        }
    }
    
    handleContinueAddApplicant() {
        this.showAddressConfirmationModal = false;
        
        // Dispatch the appropriate event based on which button was clicked
        const eventName = this.lastButtonClicked === 'next' ? EVENTS.NEXT : EVENTS.ADD_APPLICANT;
        this.dispatchEvent(new CustomEvent(eventName, { 
            detail: this._applicationData,
            bubbles: true,
            composed: true
        }));
    }
    
    handleCancelAddApplicant() {
        this.showAddressConfirmationModal = false;
    }

    // Add these methods for the ID type dropdown
    updateSelectedIdTypeLabel() {
        const idType = this._applicationData?.IdentityInfo?.Type;
        this.selectedIdTypeLabel = AddressIdHelper.getIdTypeLabel(idType, this.groupedIdTypeOptions);
    }

    handleIdTypeClick() {
        this.isIdTypeDropdownOpen = !this.isIdTypeDropdownOpen;
    }

    handleIdTypeBlur() {
        // Use setTimeout to allow click events to fire before closing dropdown
        setTimeout(() => {
            this.isIdTypeDropdownOpen = false;
        }, 300);
    }

    handleIdTypeOptionClick(event) {
        const value = event.currentTarget.dataset.value;
        const label = event.currentTarget.dataset.label;
        
        // Update the data model
        this.ensureDataStructure();
        this._applicationData.IdentityInfo.Type = value;
        
        // Update the UI
        this.selectedIdTypeLabel = label;
        this.isIdTypeDropdownOpen = false;
        
        // Set field visibility based on ID type using helper
        this.updateFieldVisibility(value);
    }

    // Add these properties to track which fields should be displayed
    @track showIdNumber = false;
    @track showIdState = false;
    @track showIdCountry = false;
    @track showIdIssueDate = false;
    @track showIdExpirationDate = false;
    @track idNumberLabel = 'ID Number';

    // Update field visibility method to use helper
    updateFieldVisibility(idType) {
        const fieldVisibility = AddressIdHelper.determineIdFieldVisibility(idType);
        
        // Apply field visibility settings to component
        this.showIdNumber = fieldVisibility.showIdNumber;
        this.showIdState = fieldVisibility.showIdState;
        this.showIdCountry = fieldVisibility.showIdCountry;
        this.showIdIssueDate = fieldVisibility.showIdIssueDate;
        this.showIdExpirationDate = fieldVisibility.showIdExpirationDate;
        this.idNumberLabel = fieldVisibility.idNumberLabel;
    }
}







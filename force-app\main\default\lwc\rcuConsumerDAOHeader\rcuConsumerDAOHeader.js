import { LightningElement, api } from 'lwc';
import COMPANY_LOGO from '@salesforce/resourceUrl/CDAO_RedwoodLogo';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';

export default class RcuConsumerDAOHeader extends LightningElement {
    imageUrl = COMPANY_LOGO;
    
    @api currentScreen;
    
    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                
            })
            .catch(error => {
                
            });
    }
    
    get showProgressTracker() {
        return this.currentScreen === 3; // Show only for primaryMemberInfoScreen (screen 3)
    }

    handleScreenClick(event) {
        const screen = event.currentTarget.dataset.screen;
        
        // Dispatch an event to notify the parent component about the screen navigation
        const navigateEvent = new CustomEvent('screennavigate', {
            detail: { screen: screen }
        });
        
        this.dispatchEvent(navigateEvent);
    }
}



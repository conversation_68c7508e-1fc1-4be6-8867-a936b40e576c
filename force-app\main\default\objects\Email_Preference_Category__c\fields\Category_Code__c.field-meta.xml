<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Category_Code__c</fullName>
    <label>Category Code</label>
    <required>true</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <controllingField>Category_Group_Code__c</controllingField>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>AUTOSERVS</fullName>
                <default>false</default>
                <label>AUTOSERVS</label>
            </value>
            <value>
                <fullName>BUSSERVS</fullName>
                <default>false</default>
                <label>BUSSERVS</label>
            </value>
            <value>
                <fullName>CARDBENE</fullName>
                <default>false</default>
                <label>CARDBENE</label>
            </value>
            <value>
                <fullName>CHKSAVINGS</fullName>
                <default>false</default>
                <label>CHKSAVINGS</label>
            </value>
            <value>
                <fullName>COMMEVENTS</fullName>
                <default>false</default>
                <label>COMMEVENTS</label>
            </value>
            <value>
                <fullName>FINWELLNESS</fullName>
                <default>false</default>
                <label>FINWELLNESS</label>
            </value>
            <value>
                <fullName>INSURANCE</fullName>
                <default>false</default>
                <label>INSURANCE</label>
            </value>
            <value>
                <fullName>LOANS</fullName>
                <default>false</default>
                <label>LOANS</label>
            </value>
            <value>
                <fullName>NEWACCOUNT</fullName>
                <default>false</default>
                <label>NEWACCOUNT</label>
            </value>
            <value>
                <fullName>NEWSLETTER</fullName>
                <default>false</default>
                <label>NEWSLETTER</label>
            </value>
            <value>
                <fullName>PREAPPROVE</fullName>
                <default>false</default>
                <label>PREAPPROVE</label>
            </value>
            <value>
                <fullName>SERVANNOUNCE</fullName>
                <default>false</default>
                <label>SERVANNOUNCE</label>
            </value>
            <value>
                <fullName>SURVEYS</fullName>
                <default>false</default>
                <label>SURVEYS</label>
            </value>
            <value>
                <fullName>WEALTHMGT</fullName>
                <default>false</default>
                <label>WEALTHMGT</label>
            </value>
        </valueSetDefinition>
        <valueSettings>
            <controllingFieldValue>ABOUTACCT</controllingFieldValue>
            <valueName>CARDBENE</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTACCT</controllingFieldValue>
            <valueName>NEWACCOUNT</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTACCT</controllingFieldValue>
            <valueName>SERVANNOUNCE</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTACCT</controllingFieldValue>
            <valueName>SURVEYS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTRCU</controllingFieldValue>
            <valueName>COMMEVENTS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTRCU</controllingFieldValue>
            <valueName>FINWELLNESS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>ABOUTRCU</controllingFieldValue>
            <valueName>NEWSLETTER</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>AUTOSERVS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>BUSSERVS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>CHKSAVINGS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>INSURANCE</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>LOANS</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>PREAPPROVE</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>PRODSERVICE</controllingFieldValue>
            <valueName>WEALTHMGT</valueName>
        </valueSettings>
    </valueSet>
</CustomField>

<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>SecuritiesFinancialInstitution__c</fullName>
    <label>Securities Financial Institution</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>An issuer of registered securities</fullName>
                <default>false</default>
                <label>An issuer of registered securities</label>
            </value>
            <value>
                <fullName>Securities and Exchange Commission (SEC)-registered investment company</fullName>
                <default>false</default>
                <label>Securities and Exchange Commission (SEC)-registered investment company</label>
            </value>
            <value>
                <fullName>SEC-registered investment adviser</fullName>
                <default>false</default>
                <label>SEC-registered investment adviser</label>
            </value>
            <value>
                <fullName>Registered exchange or clearing agency</fullName>
                <default>false</default>
                <label>Registered exchange or clearing agency</label>
            </value>
            <value>
                <fullName>Other SEC-registered organization</fullName>
                <default>false</default>
                <label>Other SEC-registered organization</label>
            </value>
            <value>
                <fullName>Registered with the Commodity Futures Trading Commission</fullName>
                <default>false</default>
                <label>Registered with the Commodity Futures Trading Commission</label>
            </value>
            <value>
                <fullName>None of the Above</fullName>
                <default>false</default>
                <label>None of the Above</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>

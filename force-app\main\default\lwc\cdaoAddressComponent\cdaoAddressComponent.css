.address-component-container {
    margin-bottom: 1rem;
}

.address-input-row {
    margin-bottom: 1rem;
}

.address-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.address-input-field {
    width: 100%;
}

.error-message {
    color: #c23934;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

/* Address Search Styling */
.address-component {
    position: relative;
}

.address-field {
    position: relative;
    margin-bottom: 0.75rem;
}

.address-suggestions-dropdown {
    position: absolute;
    z-index: 9999;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: white !important;
    border: 1px solid #dddbda !important;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-top: -1px;
}

.slds-listbox__option {
    padding: 0.5rem;
    cursor: pointer;
    color: #000000 !important; /* Force black text color */
    background-color: #ffffff !important; /* Force white background */
    display: block !important; /* Ensure the element is displayed as a block */
    text-align: left !important; /* Ensure text is left-aligned */
}

.slds-listbox__option:hover {
    background-color: #f3f2f2 !important;
}

.slds-truncate {
    color: #000000 !important; /* Force black text color */
    display: block !important; /* Ensure the element is displayed */
    white-space: normal !important; /* Allow text to wrap */
}

.search-spinner {
    position: absolute;
    right: 1rem;
    top: 2rem;
}

/* Fix for dropdown positioning */
.slds-dropdown {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    display: block !important; /* Ensure the dropdown is displayed */
}

/* Make sure list items are visible */
.slds-listbox__item {
    display: block !important;
    visibility: visible !important;
}

/* Ensure the listbox is visible */
.slds-listbox {
    display: block !important;
    visibility: visible !important;
    background-color: white !important;
    color: black !important;
    border: 1px solid #dddbda !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Additional styles to ensure visibility */
.address-suggestions-dropdown ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.address-suggestions-dropdown li {
    padding: 8px 12px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    cursor: pointer !important;
    color: #000 !important;
    background: #fff !important;
}

.address-suggestions-dropdown li:hover {
    background-color: #f3f2f2 !important;
}

/* Force text to be visible */
.address-suggestions-dropdown * {
    color: #000 !important;
}

/* Custom suggestion list styles */
.custom-suggestions-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
    background-color: white !important;
}

.custom-suggestion-item {
    padding: 10px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    cursor: pointer !important;
    color: #000 !important;
    background: #fff !important;
    display: block !important;
    font-size: 14px !important;
    text-align: left !important;
}

.custom-suggestion-item:hover {
    background-color: #f3f2f2 !important;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .address-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Adjust spacing inside the card */
.slds-p-horizontal_medium {
    padding-top: 0.5rem;
}

/* Ensure the dropdown appears above the card */
.address-suggestions-dropdown {
    position: absolute;
    z-index: 9999;
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
    background: white !important;
    border: 1px solid #dddbda !important;
    border-radius: 0.25rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-top: -1px;
}









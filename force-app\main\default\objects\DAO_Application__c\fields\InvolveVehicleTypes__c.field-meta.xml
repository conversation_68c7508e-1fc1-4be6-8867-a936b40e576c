<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>InvolveVehicleTypes__c</fullName>
    <label>Involve Vehicle Types</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Airplanes</fullName>
                <default>false</default>
                <label>Airplanes</label>
            </value>
            <value>
                <fullName>ATVs</fullName>
                <default>false</default>
                <label>ATVs</label>
            </value>
            <value>
                <fullName>Boats</fullName>
                <default>false</default>
                <label>Boats</label>
            </value>
            <value>
                <fullName>Commercial Vehicles</fullName>
                <default>false</default>
                <label>Commercial Vehicles</label>
            </value>
            <value>
                <fullName>Motorcycles</fullName>
                <default>false</default>
                <label>Motorcycles</label>
            </value>
            <value>
                <fullName>Personal Vehicles (Cars/Trucks)</fullName>
                <default>false</default>
                <label>Personal Vehicles (Cars/Trucks)</label>
            </value>
            <value>
                <fullName>Recreational Vehicles (RVs)</fullName>
                <default>false</default>
                <label>Recreational Vehicles (RVs)</label>
            </value>
            <value>
                <fullName>Sports Utility Vehicles</fullName>
                <default>false</default>
                <label>Sports Utility Vehicles</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>

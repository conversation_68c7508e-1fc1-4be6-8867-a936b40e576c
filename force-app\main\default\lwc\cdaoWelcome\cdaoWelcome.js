import { LightningElement, api } from 'lwc';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';

export default class CdaoWelcome extends LightningElement {
    @api applicationData;

    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                //logic here
            })
            .catch(error => {
                //logic here
            });

        if (this.applicationData) {
        }
        
    }

    handleGetStarted() {
        //TODO add logic to check the options selected and set the next screen accoordingly
        let objEventDetails = { started: true, newApplication: true, nextScreen : 'questionScreen' };
        this.dispatchEvent(new CustomEvent('next', {
            detail: {
                nextScreen : 'questionScreen',
                screenData: { started: true, newApplication: true }
            }
        }));
    }

    handleContinueApplication() {
        this.dispatchEvent(new CustomEvent('next', { 
            detail: {
                nextScreen : 'questionScreen',
                screenData: { started: true, newApplication: true }
            }
        }));
    }
}
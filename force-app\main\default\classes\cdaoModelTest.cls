@isTest
private class cdaoModelTest {

    @isTest
    static void testAllFieldsInCdaoModel() {
        cdaoModel model = new cdaoModel();

        cdaoModel.Application app = new cdaoModel.Application();
        app.primaryApplicant = new cdaoModel.Applicant();
        app.jointApplicants = new List<cdaoModel.Applicant>();
        app.products = new cdaoModel.Products();
        app.fundingOptions = new cdaoModel.FundingOptions();

        // Primary Applicant
        cdaoModel.Applicant primary = new cdaoModel.Applicant();
        primary.addionalInfromation = new cdaoModel.AddionalInfromation();
        primary.MemberInformation = new cdaoModel.MemberInformation();
        primary.MemberInformation.SSN_TIN = '***********';
        primary.MemberInformation.First_Name = 'John';
        primary.MemberInformation.Middle_Name = 'M';
        primary.MemberInformation.Individual_Role = 'PRimary';
        primary.MemberInformation.Last_Name = 'Doe';
        primary.MemberInformation.Date_of_Birth = '1990-01-01';
        primary.MemberInformation.Suffix = 'Jr.';
        primary.MemberInformation.Phone = '********90';
        primary.MemberInformation.Email_Address = '<EMAIL>';

        primary.MailingAddress = new cdaoModel.MailingAddress();
        primary.MailingAddress.Mailing_Street_Address = '123 testing';
        primary.MailingAddress.Mailing_City = 'test';
        primary.MailingAddress.Mailing_State = 'CA';
        primary.MailingAddress.Mailing_Zip_code = '95401';

        primary.PhysicalAddress = new cdaoModel.PhysicalAddress();
        primary.PhysicalAddress.Physical_Street_Address = 'test';
        primary.PhysicalAddress.Physical_City = 'test';
        primary.PhysicalAddress.Physical_State = 'CA';
        primary.PhysicalAddress.Physical_Zip_code = '95402';

        primary.Mailing_Address_same_as_Physical = false;

        primary.IdentityInfo = new cdaoModel.Identity();
        primary.IdentityInfo.Type = 'Driver License';
        primary.IdentityInfo.ID_Number = 'D000000';
        primary.IdentityInfo.ID_Issued_Date = '2015-01-01';
        primary.IdentityInfo.ID_Expiration_Date = '2025-01-01';
        primary.IdentityInfo.ID_Country = 'USA';
        primary.IdentityInfo.ID_State = 'CA';
        app.primaryApplicant = primary;

        // Additional Information
        cdaoModel.AddionalInfromation info = new cdaoModel.AddionalInfromation();
        info.Verbal_Password = 'secret';
        info.Verbal_Password_Hint = 'pet name';
        info.Membership_Eigblity = 'Work';
        info.County = 'test';
        info.Member_Name = 'Parent Doe';
        info.Relation_Type = 'Parent';
        info.Housing_Status = 'Rent';
        info.Collage_Student = 'Yes';
        info.Employment_Status = 'Employed';
        info.Job_Title = 'Engineer';
        info.RCU_Employee = 'No';
        info.Employer_Name = 'testing';
        info.Employment_Years = '5';
        info.Employment_Months = '6';
        info.Type_Of_Business = 'Software';
        info.Monthly_Income = '6000';
        info.Monthly_Payment = '1000';
        info.isAdditional_Monthly_Income = true;
        info.Additional_Monthly_Income = '500';
        info.IRS_Withholding = true;
        primary.addionalInfromation = info;

         // Joint Applicants
        cdaoModel.Applicant joint = new cdaoModel.Applicant();
        joint.addionalInfromation = info;
        joint.MemberInformation = new cdaoModel.MemberInformation();
        joint.MemberInformation.First_Name = 'Jane';
        app.jointApplicants.add(joint);

        // Products
        cdaoModel.Products prod = new cdaoModel.Products();
        prod.Savings = true;
        prod.Benifits_Checking = true;
        prod.Regular_Checking = true;
        prod.Plus_Checking = false;
        prod.Direct_Access_Checking = true;
        prod.Debit_Card = true;
        prod.Digital_Banking = true;
        prod.eStatement = true;
        prod.Credit_Card = false;
        prod.All_OPA = false;
        prod.Checks_Electronics_OPA = true;
        prod.No_OPA = false;
        app.products = prod;

        // Funding Options
        cdaoModel.FundingOptions funding = new cdaoModel.FundingOptions();
        funding.Funding_Type = 'External';
        funding.Savings_Deposit_Amount = '200';
        funding.Checking_Deposit_Amount = '500';
        funding.Internal_Transfer_Account_Type = 'Checking';
        funding.Internal_Transfer_Account_Number = '********';
        funding.External_Transfer_Account_Type = 'Savings';
        funding.External_Transfer_Account_Number = '********';
        funding.External_Transfer_State = 'CA';
        funding.External_Transfer_Name_On_Account = 'John Doe';
        funding.External_Transfer_Routing_Number = '*********';
        funding.External_Transfer_Bank_Name = 'Bank of America';
        funding.CC_Number = '****************';
        funding.CC_Holder_Name = 'test test';
        funding.CC_Expiration_Date = '12/30';
        funding.CC_CVV = '123';

        funding.BillingAddress = new cdaoModel.Address();
        funding.BillingAddress.Street_Address = 'testing street';
        funding.BillingAddress.City = 'test Rosa';
        funding.BillingAddress.State = 'CA';
        funding.BillingAddress.Zip_code = '95403';

        app.fundingOptions = funding;

        // Asserts to verify a few values
        System.assertEquals('John', app.primaryApplicant.MemberInformation.First_Name);
        System.assertEquals(true, app.products.Savings);
        System.assertEquals('External', app.fundingOptions.Funding_Type);
        System.assertEquals('testing street', app.fundingOptions.BillingAddress.Street_Address);
        System.assertEquals('testing', app.primaryApplicant.addionalInfromation.Employer_Name);

    }
}
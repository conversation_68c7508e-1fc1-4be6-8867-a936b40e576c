// Apex class to handle address auto-completion using SmartyStreets API
// This class is used in the cdaoAddressAutoComplete component to fetch address suggestions based on user input.
// It uses the SmartyStreets API to get address suggestions and returns them in a format suitable for the component.
public with sharing class cdaoAddressAutoComplete {
    @AuraEnabled
    public static string getAddress(String search){
        try {
            search = search.replace(' ','%20');
            String authId = System.Label.SmartyAuthId;
            String token = System.Label.SmartyToken; 
            String endpoint = System.Label.SmartyEndpoint; 
            String url = endpoint+authId+'&auth-token='+token+'&search='+search;
            
            System.debug('Calling Smarty API with URL: ' + url);
            
            HttpRequest req = new HttpRequest();
            req.setEndpoint(url);
            req.setMethod('GET');
            Http http = new Http();
            HTTPResponse res = http.send(req);
            
            System.debug('API Response Status: ' + res.getStatusCode());
            System.debug('API Response Body: ' + res.getBody());
            
            return res.getBody();

        } catch (Exception e) {
            System.debug('Exception in getAddress: ' + e.getMessage());
            throw new AuraHandledException(e.getMessage());
        }
    }
}

import { LightningElement } from 'lwc';
import RIGHT_BACKGROUND_IMAGE from '@salesforce/resourceUrl/CDAO_RightBackgroundImage';
import LEFT_BACKGROUND_IMAGE from '@salesforce/resourceUrl/CDAO_LeftBackgroundImage';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';

import getApplication from '@salesforce/apex/rcuConsumerDAO.getApplication';
import saveApplication from '@salesforce/apex/rcuConsumerDAO.saveApplication';

// Import constants
import { 
    SCREENS, 
    SCREEN_SEQUENCE,  
    SCREENS_REQUIRING_SAVE,
    ERROR_MESSAGES,
    DEFAULT_APPLICATION_DATA,
    FIELD_MAPPINGS,
    CSS_CLASSES,
    EVENTS
} from './rcuConsumerDAOConstants';

export default class RcuConsumerDao extends LightningElement {
    // Core properties
    rightBackgroundImageUrl = RIGHT_BACKGROUND_IMAGE;
    leftBackgroundImageUrl = LEFT_BACKGROUND_IMAGE;
    
    // Screen management
    screens = SCREEN_SEQUENCE.slice(0, 4); // Only use the first 4 screens for now
    currentScreenIndex = 0;
    previousScreen = 0;
    
    // Application data - will be populated from server
    applicationData = JSON.parse(JSON.stringify(DEFAULT_APPLICATION_DATA));
    
    // Lifecycle hooks
    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {})
            .catch(error => {});
        
        // Load application data from server
        this.loadApplicationData();
    }
    
    // Load data from server
    loadApplicationData() {
        return new Promise((resolve, reject) => {
            getApplication()
                .then(result => {
                    this.applicationData.application = result;
                    resolve(result);
                })
                .catch(error => {
                    console.error('Error loading application data:', error);
                    reject(error);
                });
        });
    }
    
    
    
    // Direct handler for member info
    handleSaveInfo(event) {
        if (event && event.detail) {
            try {
                // If application doesn't exist, load it first
                if (!this.applicationData.application) {
                    // Load application data from server first
                    this.loadApplicationData()
                        .then(() => {
                            // Then update and save the data
                            this.updateApplicationData(SCREENS.PRIMARY_MEMBER, event.detail);
                        })
                        .catch(error => {
                            console.error('Error loading application data:', error);
                        });
                } else {
                    // Application exists, update and save directly
                    this.updateApplicationData(SCREENS.PRIMARY_MEMBER, event.detail);
                }
            } catch (error) {
                console.error('Error in handleSaveInfo:', error);
            }
        }
    }
    
    get currentScreenNumber() {
        return this.currentScreenIndex + 1;
    }

    get isWelcomePage() {
        return this.screens[this.currentScreenIndex] === SCREENS.WELCOME;
    }

    get isQuestionScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.QUESTIONS;
    }

    get isPrimaryMemberInfoScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.PRIMARY_MEMBER;
    }

    get isAddressAndIdScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.ADDRESS_ID;
    }

    get getPrimaryMemberData() {
        return this.applicationData.application.primaryApplicant;
    }

   // Handle next event from any screen
    handleNext(event) {
        const currentScreen = this.screens[this.currentScreenIndex];
        const screenData = event.detail.screenData || {};
        const nextScreen = event.detail.nextScreen || currentScreen;
        this.previousScreen = this.currentScreenIndex;
        this.currentScreenIndex = this.screens.indexOf(nextScreen);
        this.updateApplicationData(currentScreen, screenData);
    }


    handleBack(event) {
        const nextScreen = event.detail.nextScreen || this.screens[this.currentScreenIndex];
        this.currentScreenIndex = this.screens.indexOf(nextScreen);
    }

    handleScreenNavigation(event) {
        const screenName = event.detail.screen;
        const screenIndex = this.screens.indexOf(screenName);
        
        if (screenIndex !== -1) {
            this.currentScreenIndex = screenIndex;
        }
    }

    handleAddApplicant(event) {
        // Implement the logic to add an applicant
    }


    renderedCallback() {
        // Set background image URLs dynamically
        const rightBgElement = this.template.querySelector(`.${CSS_CLASSES.RIGHT_BG}`);
        const leftBgElement = this.template.querySelector(`.${CSS_CLASSES.LEFT_BG}`);
        
        if (rightBgElement) {
          rightBgElement.style.backgroundImage = `url(${this.rightBackgroundImageUrl})`;
        }
        
        if (leftBgElement) {
          leftBgElement.style.backgroundImage = `url(${this.leftBackgroundImageUrl})`;
        }
    }


    // Internal helper to update specific parts of the application
    updateApplicationData(screenName, screenData) {
        // // Create a new application object to avoid proxy issues
         //const newApplication = JSON.parse(JSON.stringify(this.applicationData.application));
         let newApplication = { ...this.applicationData.application };
        // console.log('Updating application data for screen:', screenName);
        // console.log('Screen data:', screenData);
        // // Ensure primaryApplicant exists
        // if (!newApplication.primaryApplicant) {
        //     newApplication.primaryApplicant = {};
        // }

       // Ensure primaryApplicant exists on newApplication
        if (!newApplication.primaryApplicant) {
            newApplication.primaryApplicant = {};
        }
        newApplication.primaryApplicant.MemberInformation = JSON.parse(JSON.stringify(screenData));
        
        // Update the appropriate section based on screen name
        switch(screenName) {
            case SCREENS.PRIMARY_MEMBER:
                console.log('Updating primary member information');
                newApplication.primaryApplicant.MemberInformation = JSON.parse(JSON.stringify(screenData));
                console.log('Primary member data:', newApplication.primaryApplicant.MemberInformation);
                break;
                
            case SCREENS.ADDRESS_ID:
                const addressFields = FIELD_MAPPINGS.ADDRESS_ID;
                newApplication.primaryApplicant[addressFields.PHYSICAL_ADDRESS] = screenData[addressFields.PHYSICAL_ADDRESS];
                newApplication.primaryApplicant[addressFields.MAILING_ADDRESS] = screenData[addressFields.MAILING_ADDRESS];
                newApplication.primaryApplicant[addressFields.SAME_ADDRESS] = screenData[addressFields.SAME_ADDRESS];
                newApplication.primaryApplicant[addressFields.IDENTITY_INFO] = screenData[addressFields.IDENTITY_INFO];
                break;
                
            case SCREENS.WELCOME:
                // Handle welcome page data if needed
                break;
                
            case SCREENS.QUESTIONS:
                const questionFields = FIELD_MAPPINGS.QUESTIONS;
                newApplication[questionFields.MEMBERSHIP_STATUS] = screenData[questionFields.MEMBERSHIP_STATUS];
                newApplication[questionFields.ACCOUNT_TYPE] = screenData[questionFields.ACCOUNT_TYPE];
                break;
        }
        
        // Update the application data with the new object
        this.applicationData.application = JSON.parse(JSON.stringify(newApplication));

        if (SCREENS_REQUIRING_SAVE.includes(screenName)) {
            // Deep clone the data to avoid reference issues
                const appData = JSON.parse(JSON.stringify(this.applicationData.application));
                const applicationDataJson = JSON.stringify(appData);
                console.log('Preparing application data for save:', applicationDataJson);
                
                saveApplication({ applicationData: applicationDataJson })
        }

    }

    // Save data to server
    // saveApplicationData() {
    //     return new Promise((resolve, reject) => {
    //         try {
    //             // Make sure we have data to save
    //             if (!this.applicationData.application) {
    //                 console.error(ERROR_MESSAGES.NO_APPLICATION);
    //                 reject(ERROR_MESSAGES.NO_APPLICATION);
    //                 return;
    //             }
                
    //             // Deep clone the data to avoid reference issues
    //             const appData = JSON.parse(JSON.stringify(this.applicationData.application));
    //             const applicationDataJson = JSON.stringify(appData);
    //             console.log('Preparing application data for save:', applicationDataJson);
                
    //             saveApplication({ applicationData: applicationDataJson })
    //                 .then(result => {
    //                     if (result && result.startsWith && result.startsWith('Error:')) {
    //                         console.error('Apex returned error:', result);
    //                         reject(result);
    //                     } else {
    //                         resolve(result);
    //                     }
    //                 })
    //                 .catch(error => {
    //                     console.error('Error in Apex saveApplication call:', error);
    //                     reject(error);
    //                 });
    //         } catch (error) {
    //             console.error('Error preparing application data:', error);
    //             reject('Error preparing application data: ' + error.message);
    //         }
    //     });
    // }

    // Update loadApplicationData to return a promise for chaining
    loadApplicationData() {
        return new Promise((resolve, reject) => {
            getApplication()
                .then(result => {
                    this.applicationData.application = result;
                    resolve(result);
                })
                .catch(error => {
                    console.error('Error loading application data:', error);
                    reject(error);
                });
        });
    }


}


















/* Header styles */
.rcuHeader-container {
    width: 100%;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
}

.rcuHeader-header {
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.rcuHeader-logo {
    height: 80px;
    max-width: 200px;
    object-fit: contain;
    margin-left: 10%;
}

.rcuHeader-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin-left: 1rem;
}

.rcuHeader-progress-indicator {
    display: flex;
    justify-content: center;
    padding: 0.5rem 0;
    background-color: #f8f8f8;
    border-bottom: 1px solid #e0e0e0;
}

.rcuHeader-custom-header {
    max-width: 100%;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    padding: 1rem 0;
    position: relative;
}

.rcuHeader-rcu-logo {
    height: 80px;
    max-width: 200px;
    object-fit: contain;
    margin-left: 10%;
    position: relative;
    z-index: 2;
}

.rcuHeader-progress-tracker {
    position: absolute;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    z-index: 1;
}

.rcuHeader-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #999999;
    font-size: 11px;
    text-align: center;
    line-height: 1.2;
}

.rcuHeader-screen lightning-icon {
    --slds-c-icon-color-foreground: #999999;
}

.rcuHeader-screen.completed {
    color: #137333;
}

.rcuHeader-screen.completed lightning-icon {
    --slds-c-icon-color-foreground: #137333;
}

.rcuHeader-screen-number {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #e0e0e0;
    color: #6b6b6b;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.rcuHeader-screen.active {
    color: #0070d2;
    font-weight: 600;
}

.rcuHeader-screen.active .rcuHeader-screen-number {
    background-color: #0070d2;
    color: white;
}

.rcuHeader-screen.completed .rcuHeader-screen-number {
    background-color: #04844b;
    color: white;
}

.rcuHeader-screen-separator {
    width: 20px;
    height: 1px;
    border-top: 1px dashed #ccc;
}

.rcuHeader-clickable {
    cursor: pointer;
    transition: transform 0.2s ease;
}

.rcuHeader-clickable:hover {
    transform: scale(1.05);
}

.rcuHeader-clickable:active {
    transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .rcuHeader-header {
        padding: 0.75rem;
    }
    
    .rcuHeader-logo {
        height: 32px;
    }
    
    .rcuHeader-custom-header {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .rcuHeader-rcu-logo {
        height: 40px;
        margin-bottom: 1rem;
    }
    
    .rcuHeader-progress-tracker {
        position: relative;
        margin-top: 1rem;
        justify-content: flex-start;
        overflow-x: auto;
        width: 100%;
        padding-bottom: 0.5rem;
    }
}

/* Add padding to body or main container to prevent content from hiding under fixed header */
:host {
    display: block;
}

/* Adjust the main content container in parent components to account for fixed header */
.rcuHeader-content-adjustment {
    padding-top: 100px; /* Adjust based on your header height */
}


/*Footer Style*/
.rcuFooter-container {
    width: 100%;
    background-color: #f8f8f8;
    border-top: 1px solid #e5e5e5;
    padding: 1rem 0;
    position: relative;
    z-index: 5;
}

.rcuFooter-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 0.875rem;
    color: #666666;
}

.rcuFooter-copyright, .rcuFooter-routing, .rcuFooter-insurance {
    margin-right: 0.5rem;
}

.rcuFooter-separator {
    margin: 0 0.5rem;
    color: #999999;
}

.rcuFooter-help-section {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.rcuFooter-help-icon {
    --slds-c-icon-color-foreground: #666666;
}

.rcuFooter-help-text {
    font-weight: 500;
}

/* Hover effects */
.rcuFooter-help-section:hover {
    color: #333333;
}

.rcuFooter-help-section:hover .rcuFooter-help-icon {
    --slds-c-icon-color-foreground: #333333;
}

/* Responsive design */
@media screen and (max-width: 768px) {
    .rcuFooter-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
    }

    .rcuFooter-separator {
        display: none;
    }

    .rcuFooter-help-section {
        margin-left: 0;
        margin-top: 0.5rem;
        width: 100%;
        justify-content: flex-start;
    }
}


/*welcome page styles*/
/* Add container styling to ensure footer stays at bottom */
.welcome-container {
    font-family: Arial, sans-serif;
    width: 100%;
    margin: 0;
    padding: 2rem 0;
    background: white;
    padding-left: 10%;
    padding-right: 10%;
    position: relative;
    min-height: calc(100vh - 100px); /* Subtract header height */
    display: flex;
    flex-direction: column;
}

/* Ensure content appears above any parent background images */
h1, p, .welcome-info-box, .welcome-buttons {
    position: relative;
    z-index: 1;
}

h1 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #137333;
}

p {
    margin: 0.5rem 0;
    line-height: 1.6;
    max-width: 65%;;
}

.welcome-highlight {
    color: #137333;
    font-weight: bold;
}

.welcome-info-box {
    max-width: 65%;
    background: #f9f9f9;
    border-left: 4px solid #137333;
    padding: 1rem;
    margin: 2rem 0;
    text-align: left;
}

.welcome-info-box a {
    color: #137333;
}

.welcome-buttons {
    display: flex;
    justify-content: right;
    gap: 1rem;
    margin: 2rem;
}

.welcome-get-started-button {
    --sds-c-button-brand-color-background: #4CAF50;
    --sds-c-button-brand-color-background-hover: #2E7D32;
    --sds-c-button-brand-color-border: #2E7D32;
    --sds-c-button-text-color: white;
}

.welcome-continue {
    --sds-c-button-brand-color-background: rgb(29, 28, 28);
    --sds-c-button-text-color: #137333;
    --sds-c-button-color-border: #137333;
    min-width: 200px;
}

a {
    color: #137333;
    text-decoration: underline;
}

/* Responsive design matching other pages */
@media screen and (max-width: 768px) {
    .welcome-container {
        width: 100%;
        max-width: 100%;
        padding: 1rem;
    }

    .welcome-buttons {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .welcome-buttons span {
        text-align: center;
        margin: 0.5rem 0;
    }
}


/*retrieve application styles*/

.NewApp-container {
    font-family: Arial, sans-serif;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 2rem 0;
    padding-left: 10%;
    padding-right: 10%;
    min-height: calc(100vh - 100px); /* Subtract header height */
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
}
  
.slds-text-heading_medium {
  font-weight: bold;
    font-size: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    color: #16325c !important;
}
  
label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #16325c;
}
  
.NewApp-info-box {
    background: #f9f9f9;
    padding: 1.5rem;
    text-align: left;
    font-size: 1rem;
    margin: 1rem 0 2rem 0;
    width: 70%;
    position: relative;
    z-index: 1;
}
  
.NewApp-info-box ul {
    padding-left: 1.2rem;
    list-style-type: disc;
}
  
.NewApp-info-box li {
    margin-bottom: 0.5rem;
}
  
.NewApp-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 3rem;
}
  
.NewApp-next {
    background: #d3d3d3;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 5px;
    cursor: not-allowed;
}
  
.NewApp-next:not([disabled]) {
    background: #137333;
    cursor: pointer;
}
  
.NewApp-next:not([disabled]):hover {
    background: #0c5023;
}
.NewApp-question-container {
  background-color: #ffffff;
  min-height: 100vh;
}
.NewApp-go-back {
    background: transparent;
    color: #137333;
    border: 1px solid #137333;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 5px;
    cursor: pointer;
}
  
.NewApp-go-back:hover {
    background: rgba(19, 115, 51, 0.1);
}
  
/* Responsive design */
@media (max-width: 768px) {
    .NewApp-container {
        max-width: 100%;
        padding: 1rem;
    }
    
    .slds-grid {
        display: block;
    }
    
    .slds-col {
        margin-bottom: 1.5rem;
    }
    
    .NewApp-buttons {
        flex-direction: column-reverse;
        align-items: stretch;
    }
    
    .NewApp-next, .NewApp-go-back {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

.NewApp-header .NewApp-logo {
    height: 60px;
    margin-bottom: 1rem;
}
.slds-form-element {
    max-width: 600px;
    margin: 0 auto;
}
  
.slds-select_container {
    max-width: 50%;
}
  
.slds-select {
    height: 2.4rem;
    
}
  
.slds-input, .slds-combobox__form-element {
    max-width: 400px;
    z-index: 1;
}
  
.NewApp-info-box ul {
    padding-left: 1.2rem;
    list-style-type: disc;
}
.NewApp-info-box li {
    margin-bottom: 0.5rem;
}
.slds-form-element__help {
    color: rgb(194, 57, 52);
    font-size: 0.75rem;
    margin-top: 0.5rem;
}
.NewApp-footer-container {
    width: 100%;
    background-color: #f8f8f8;
    border-top: 1px solid #e5e5e5;
    padding: 1rem 0;
    margin-top: auto;
}
  
.NewApp-footer-content {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 2rem;
    font-size: 0.875rem;
    color: #666666;
}
  
.NewApp-separator {
    margin: 0 10px;
    color: #999999;
}
  
.NewApp-help-section {
    margin-left: auto;
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
}
  
/* Add to existing media queries */
@media screen and (max-width: 768px) {
    .NewApp-footer-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
  
    .NewApp-separator {
        display: none;
    }
  
    .NewApp-help-section {
        margin-left: 0;
        margin-top: 1rem;
    }
}
    
.NewApp-logo {
    height: 60px;
    margin-bottom: 2rem;
}
h1 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}
label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #16325c;
}
.NewApp-dropdown {
    width: 100%;
    padding: 0.6rem;
    font-size: 0.5rem;
    margin-bottom: 2rem;
}
.NewApp-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 3rem;
}
.NewApp-next {
    background: #d3d3d3;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 5px;
    cursor: not-allowed;
}
.NewApp-next:not([disabled]) {
    background: #0070d2;
    cursor: pointer;
}
.NewApp-next:not([disabled]):hover {
    background: #005fb2;
}
.NewApp-go-back {
    background: #333;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 5px;
    cursor: pointer;
}
   
.NewApp-help {
    margin-top: 0.5rem;
}
.NewApp-icon {
    font-weight: bold;
    background: #ccc;
    border-radius: 50%;
    padding: 0 0.4rem;
    margin-right: 0.25rem;
}
.slds-grid {
    margin-bottom: 1rem;  /* Added to ensure consistent spacing */
    }


/*memberInformation style*/


.memberInfo-form-container {
    font-family: Arial, sans-serif;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 2rem 0;
    padding-left: 10%;
    padding-right: 10%;
    min-height: calc(100vh - 100px); /* Subtract header height */
    display: flex;
    flex-direction: column;
    background: white;
    position: relative;
  }
  
  h2 {
    font-size: 1.5rem;
    color: #137333;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  h3 {
    font-size: 1.25rem;
    color: #444444;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f3f3f3;
  }
  
  .memberInfo-form-box {
    background-color: #f8f8f8;
    padding: 2rem;
    margin-bottom: 2rem;
    border-left: 2px solid #012513;
    margin-right: 10%;
    z-index: 1;
  }
  
  .memberInfo-input-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .slds-input {
    display: flex;
    flex-direction: column;
  }
  
  .memberInfo-input-field label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #444;
  }
  
  .slds-input, 
  .memberInfo-input-field select {
    padding: 0.75rem;
    border: 1px solid #d8dde6;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
  }
  .memberInfo-input-row .memberInfo-input-field .fix-slds-input_faux[lwc-4kb6kaqheak],
  .addressAndIdInfo-input-row .addressAndIdInfo-input-field .fix-slds-input_faux[lwc-4kb6kaqheak]
   {
    line-height: max(1.875rem, calc(1.2em - 2px));
    background-color: #f4f1f1;
    padding: 0.6rem;
    border: 1px solid #d8dde6;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    
  }
  .fix-slds-input_faux[lwc-4kb6kaqheak] {
    line-height: max(1.875rem, calc(1.2em - 2px));
    background-color: #f4f1f1;
  }
  
  .memberInfo-input-field input:focus, 
  .memberInfo-input-field select:focus {
    outline: none;
    border-color: #137333;
    box-shadow: 0 0 3px #137333;
  }
  
  .required {
    color: #c23934;
  }
  
  .error-message {
    color: #c23934;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
  
  /* Button row styling */
  .memberInfo-button-row {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .memberInfo-save-btn {
    background-color: #137333;
    color: white;
    border: none;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .memberInfo-save-btn:hover {
    background-color: #0c5023;
  }
  
  .memberInfo-save-btn:disabled {
    background-color: #c9c9c9;
    cursor: not-allowed;
  }
  
  .memberInfo-back-btn {
    background-color: transparent;
    color: #444;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
  }
  
  .memberInfo-back-btn:hover {
    background-color: #f3f3f3;
  }
  
  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .memberInfo-form-container {
      padding: 1rem;
      max-width: 100%;
    }
  
    .memberInfo-form-box {
      padding: 1rem;
    }
  
    .memberInfo-input-row {
      grid-template-columns: 1fr;
    }
  
    .memberInfo-button-row {
      flex-direction: column-reverse;
      align-items: stretch;
    }
  
    .memberInfo-save-btn, 
    .memberInfo-back-btn {
      width: 100%;
      margin-bottom: 0.5rem;
    }
  }

/*addressAndsIdInformation styles*/
.addressAndIdInfo-form-container {
    font-family: Arial, sans-serif;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 2rem 0;
    padding-left: 10%;
    padding-right: 10%;
    min-height: calc(100vh - 100px);
    display: flex;
    flex-direction: column;
    background: white;
    position: relative;
  }
  
  /* Ensure content appears above any parent background images */
  .addressAndIdInfo-form-box, h2, h3, .addressAndIdInfo-button-row {
    position: relative;
    z-index: 1;
  }
  
  h2 {
    font-size: 1.5rem;
    color: #137333;
    margin-bottom: 1rem;
    font-weight: 600;
  }
  
  h3 {
    font-size: 1.25rem;
    color: #444444;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #f3f3f3;
  }
  
  .addressAndIdInfo-form-box {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    border-left: 4px solid #137333;
  }
  
  .addressAndIdInfo-input-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }
  
 
  /* Ensure the input element itself takes full width */
  .addressAndIdInfo-input-field lightning-input {
    width: 100%;
  }

  .addressAndIdInfo-checkbox-row {
    margin: 1rem 0;
  }

  .addressAndIdInfo-checkbox-field {
    display: flex;
    align-items: center;
  }

  .addressAndIdInfo-mailing-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }

  .addressAndIdInfo-mailing-header h3 {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }

  .addressAndIdInfo-remove-mailing-btn {
    font-size: 0.875rem;
  }

  .addressAndIdInfo-button-row {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    align-items: center;
  }

  .addressAndIdInfo-right-buttons {
    display: flex;
    gap: 1rem;
  }

  /* Style for lightning inputs to match memberInfo component */
  .addressAndIdInfo-input-field lightning-input,
  .addressAndIdInfo-input-field lightning-combobox {
    --sds-c-input-radius-border: 4px;
    --sds-c-input-color-border: #d8dde6;
    --sds-c-input-shadow-focus: 0 0 3px #0070d2;
    --sds-c-input-color-border-focus: #1589ee;
  }

  /* Error message styling */
  .error-message {
    color: #c23934;
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }

  /* Button styling to match memberInfo component */
  .addressAndIdInfo-add-applicant-button {
    --sds-c-button-brand-color-background: #137333;
    --sds-c-button-brand-color-border: #137333;
    --sds-c-button-text-color: white;
    --sds-c-button-brand-color-background-hover: #0c5023;
    --sds-c-button-radius-border: 4px;
  }

  /* Modal styling */
  .slds-modal__header {
    background-color: #f3f3f3;
    border-bottom: 1px solid #d8dde6;
  }

  .slds-modal__title {
    color: #137333;
    font-weight: 600;
  }

  .slds-modal__content {
    padding: 2rem;
  }

  .slds-modal__footer {
    border-top: 1px solid #d8dde6;
    padding: 1rem 2rem;
  }

  /* Responsive adjustments */
  @media screen and (max-width: 768px) {
    .addressAndIdInfo-form-container {
        padding: 1rem;
    }
    
    .addressAndIdInfo-form-box {
        padding: 1rem;
    }
    
    .addressAndIdInfo-input-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .addressAndIdInfo-button-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .addressAndIdInfo-right-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }
  }
  
  /* Add these styles for the address confirmation modal */
  .addressAndIdInfo-address-box {
    border: 1px solid #137333;
    padding: 15px;
    margin: 15px 0;
    border-radius: 4px;
    background-color: #f8f8f8;
    text-align: left;
  }

  .addressAndIdInfo-verification-text {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #333;
  }

  .addressAndIdInfo-confirmation-text {
    color: #666;
    margin-top: 1rem;
  }

  .addressAndIdInfo-modal-title {
    color: #137333;
    text-align: center;
  }

  .addressAndIdInfo-continue-btn {
    background-color: #137333;
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    width: 100%;
    margin-bottom: 10px;
  }

  .addressAndIdInfo-continue-btn:hover {
    background-color: #0c5023;
  }

  .addressAndIdInfo-cancel-btn {
    background-color: #f8f8f8;
    color: #444;
    border: 1px solid #ddd;
    padding: 0.75rem;
    border-radius: 4px;
    cursor: pointer;
    width: 100%;
  }

  .addressAndIdInfo-cancel-btn:hover {
    background-color: #eee;
  }
  
  /* ID Type Dropdown Styles */
  .addressAndIdInfo-id-type-dropdown{
    z-index: 99;
  }
  .addressAndIdInfo-id-type-dropdown .slds-listbox__option-header {
    font-weight: bold;
    color: #706e6b;
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  .addressAndIdInfo-id-type-dropdown .slds-has-divider_bottom-space {
    margin-top: 0.5rem;
    border-bottom: 1px solid #dddbda;
    padding-bottom: 0.25rem;
  }

  .addressAndIdInfo-id-type-dropdown .slds-dropdown {
    max-height: 300px;
    overflow-y: auto;
    z-index: 99;
  }

  .addressAndIdInfo-id-type-dropdown .slds-listbox__option:hover {
    background-color: #f3f2f2;
    cursor: pointer;
  }

  .addressAndIdInfo-id-type-dropdown .slds-combobox__input {
    padding: 0.75rem;
    border: 1px solid #d8dde6;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
  }
  
  
  
/*rcuConsumerDAO page style*/

:host {
  display: block;
  width: 100%;
  height: 100%;
}

.rcu-content-adjustment {
  padding-top: 100px; 
}

.rcu-timeout-warning-banner {
  background-color: #fff9c4;
  color: #333;
  padding: 10px 20px;
  text-align: center;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rcu-warning-content {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
  font-size: 14px;
}

.rcu-warning-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}

.rcu-warning-actions a {
  color: #0070d2;
  text-decoration: none;
  font-weight: 500;
}

.rcu-warning-actions a:hover {
  text-decoration: underline;
}

.rcu-separator {
  color: #999;
  margin: 0 5px;
}

.rcu-app-container {
  position: relative;
  min-height: 100vh;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

.rcu-right-background-image {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 300px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: top right;
  opacity: 0.5;
  z-index: 1;
  pointer-events: none;
}

.rcu-left-background-image {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 400px;
  height: 400px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: bottom left;
  opacity: 0.3;
  z-index: 1;
  pointer-events: none;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .rcu-right-background-image,
  .rcu-left-background-image {
      width: 150px;
      height: 150px;
  }
}




/* Modal styling */
.rcu-modal-container {
  max-width: 500px;
}

.rcu-modal-content {
  font-size: 16px;
  line-height: 1.5;
}

.rcu-backdrop {
  background: rgba(0, 0, 0, 0.5);
}








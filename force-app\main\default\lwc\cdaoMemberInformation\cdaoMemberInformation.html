<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 05-20-2025
  @last modified by  : <EMAIL>
-->
<template>
  <div class="memberInfo-form-container">
    <h2>{currentMemberTypeHeading}</h2>
    <h3>{currentMemberInfoHeading}</h3>

    <div class="memberInfo-form-box">
      <div class="memberInfo-input-row">
        <div class="memberInfo-input-field">
          <lightning-input 
            label="First Name" 
            type="text" 
            required
            name="First_Name"
            value={First_Name}
            onchange={handleInput}
            class="required-field"
             message-when-value-missing="Please enter your First Name"
            message-when-pattern-mismatch="Please enter only letters and spaces"
            pattern="[a-zA-Z\s]+"
          ></lightning-input>
        </div>
        
        <div class="memberInfo-input-field">
          <lightning-input 
            label="Middle Name" 
            type="text" 
            name="Middle_Name"
            value={Middle_Name}
            onchange={handleInput}
            pattern="[a-zA-Z\s]+"
            message-when-pattern-mismatch="Please enter only letters and spaces"
          ></lightning-input>
        </div>
        
        <div class="memberInfo-input-field">
          <lightning-input 
            label="Last Name" 
            type="text" 
            required
            name="Last_Name"
            value={Last_Name}
            onchange={handleInput}
            class="required-field"
            message-when-value-missing="Please enter your Last Name"
            message-when-pattern-mismatch="Please enter only letters and spaces"
            pattern="[a-zA-Z\s]+"
          ></lightning-input>
        </div>
      </div>

      <div class="memberInfo-input-row">
        <div class="memberInfo-input-field">
          <lightning-combobox
            label="Suffix"
            name="Suffix"
            value={Suffix}
            options={suffixOptions}
            onchange={handleInput}
          ></lightning-combobox>
        </div>

        <div class="memberInfo-input-field">
          <lightning-input 
            label="Date of Birth (mm/dd/yyyy)" 
            type="text"
            required
            name="Date_of_Birth"
            value={Date_of_Birth}
            onchange={handleDOBInput}
            maxlength="10"
             message-when-value-missing="Please enter your Date of Birth"
            placeholder="mm/dd/yyyy"
            class="required-field"
          ></lightning-input>
          <div if:true={Date_of_Birth_Error} class="error-message">{Date_of_Birth_Error}</div>
        </div>
        
        <div class="memberInfo-input-field">
          <lightning-input 
            label="Social Security / ITIN Number" 
            type="text"
            required
            name="SSN_TIN"
            data-id="ssn-input"
            value={displaySSN}
            onfocus={handleSSNFocus}
            onblur={handleSSNBlur}
             message-when-value-missing="Please enter your Social Security or ITIN Number"
            onchange={handleInput}
            maxlength="9"
            class="required-field"
          ></lightning-input>
        </div>
      </div>

      <div class="memberInfo-input-row">
        <div class="memberInfo-input-field">
          <lightning-input 
            label="Email" 
            type="email"
            required
            name="Email_Address"
            value={Email_Address}
            onchange={handleInput}
            class="required-field"
            message-when-value-missing="Please enter your Email Address"
            message-when-pattern-mismatch="Please enter a valid email address"
          ></lightning-input>
        </div>
        
        <div class="memberInfo-input-field">
          <lightning-input 
            label="Primary Phone Number (xxx-xxx-xxxx)" 
            type="text"
            name="Phone"
            value={Phone}
            onchange={handleInput}
            maxlength="12"
            placeholder="xxx-xxx-xxxx"
            required
             message-when-value-missing="Please enter your Primary Phone Number"
          ></lightning-input>
        </div>
      </div>
    </div>

    <div class="memberInfo-button-row">
      <lightning-button 
        label="Save and Continue"
        variant="brand"
        disabled={isSaveDisabled}
        onclick={handleNext}
        class="memberInfo-save-btn"
      ></lightning-button>
      <lightning-button 
        label="Go Back"
        variant="neutral"
        onclick={handleBack}
        class="memberInfo-back-btn"
      ></lightning-button>
    </div>
  </div>
</template>











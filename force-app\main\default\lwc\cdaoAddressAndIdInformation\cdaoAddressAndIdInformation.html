<template>
  <div class="addressAndIdInfo-form-container">
    <h2>Address &amp; ID Information</h2>
    
    <div class="addressAndIdInfo-form-box">
      <h3>Address</h3>
      <div class="addressAndIdInfo-input-row">
        <div class="addressAndIdInfo-input-field">
          <lightning-input 
            type="text" 
            label="Address"
            name="Physical_Street_Address"
            value={Physical_Street_Address} 
            onchange={handleInput}
             message-when-value-missing="Please enter your Address"
            required>
          </lightning-input>
        </div>
      </div>
      
      <div class="addressAndIdInfo-input-row">
        <div class="addressAndIdInfo-input-field">
          <lightning-input 
            type="text" 
            label="City"
            name="Physical_City"
            value={Physical_City} 
            onchange={handleInput}
             message-when-value-missing="Please enter your City"
            required>
          </lightning-input>
        </div>
        
        <div class="addressAndIdInfo-input-field">
          <lightning-combobox 
            name="Physical_State" 
            label="State" 
            value={Physical_State} 
            options={stateOptions}
             message-when-value-missing="Please enter your State" 
            onchange={handleInput}
            required>
          </lightning-combobox>
        </div>
        
        <div class="addressAndIdInfo-input-field">
          <lightning-input
            name="Physical_Zip_code"
            value={Physical_Zip_code}
            label="ZIP Code" 
            onchange={handleInput}
            pattern="[0-9]{5}"
            maxlength="5"
             message-when-value-missing="Please enter your ZIP Code"
            message-when-pattern-mismatch="Please enter a valid 5-digit ZIP code"
            required>
          </lightning-input>
          <div if:true={zipCodeError} class="error-message">{zipCodeError}</div>
        </div>
      </div>
      
      <div class="addressAndIdInfo-checkbox-row">
        <div class="addressAndIdInfo-checkbox-field">
          <lightning-input 
            type="checkbox" 
            label="Use a different mailing address" 
            checked={useDifferentMailingAddress} 
            onchange={handleCheckboxChange}>
          </lightning-input>
        </div>
      </div>
    </div>
    
    <!-- Mailing Address Section - Only visible when checkbox is checked -->
    <template if:true={useDifferentMailingAddress}>
      <div class="addressAndIdInfo-form-box addressAndIdInfo-mailing-address-box">
        <div class="addressAndIdInfo-mailing-header">
          <h3>Mailing Address</h3>
          <lightning-button
            variant="destructive-text"
            label="Remove Mailing Address"
            icon-position="left"
            icon-name="utility:close"
            class="addressAndIdInfo-remove-mailing-btn"
            onclick={handleRemoveMailingAddress}>
          </lightning-button>
        </div>
        
        <div class="addressAndIdInfo-input-row">
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              name="Mailing_Street_Address"
              label="Mailing Address"
              value={Mailing_Street_Address}
              onchange={handleInput}
               message-when-value-missing="Please enter your Address"
              required>
            </lightning-input>
          </div>
        </div>
        
        <div class="addressAndIdInfo-input-row">
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              name="Mailing_City"
              label="Mailing City"
              value={Mailing_City}
              message-when-value-missing="Please enter your City"
              onchange={handleInput}
              required>
            </lightning-input>
          </div>
          
          <div class="addressAndIdInfo-input-field">
            <lightning-combobox
              name="Mailing_State"
              label="Mailing State"
              value={Mailing_State}
              options={stateOptions}
              message-when-value-missing="Please enter your State"
              onchange={handleInput}
              required>
            </lightning-combobox>
          </div>
          
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              name="Mailing_Zip_code"
              label="Mailing ZIP Code"
              value={Mailing_Zip_code}
              onchange={handleInput}
              pattern="[0-9]{5}"
              maxlength="5"
              message-when-value-missing="Please enter your ZIP Code"
              message-when-pattern-mismatch="Please enter a valid 5-digit ZIP code"
              required>
            </lightning-input>
            <div if:true={mailingZipCodeError} class="error-message">{mailingZipCodeError}</div>
          </div>
        </div>
      </div>
    </template>
    
    <div class="addressAndIdInfo-form-box">
      <h3>Identification</h3>
      <div class="addressAndIdInfo-input-row">
        <div class="addressAndIdInfo-input-field addressAndIdInfo-id-type-dropdown">
          <label class="slds-form-element__label" for="id-type-combobox">
            <abbr class="slds-required" title="required">*</abbr>
            ID Type
          </label>
          <div class="slds-form-element__control">
            <div class="slds-combobox_container">
              <div class="slds-combobox slds-dropdown-trigger slds-dropdown-trigger_click" 
                   aria-expanded={isIdTypeDropdownOpen} 
                   aria-haspopup="listbox" 
                   role="combobox">
                <div class="slds-combobox__form-element slds-input-has-icon slds-input-has-icon_right" 
                     role="none">
                  <input type="text"
                         id="id-type-combobox"
                         class="slds-input slds-combobox__input"
                         aria-controls="id-type-listbox"
                         autocomplete="off"
                         role="textbox"
                         placeholder="Select an ID Type"
                         readonly
                         value={selectedIdTypeLabel}
                         onclick={handleIdTypeClick}
                         onblur={handleIdTypeBlur}
                         required/>
                  <span class="slds-icon_container slds-icon-utility-down slds-input__icon slds-input__icon_right">
                    <lightning-icon icon-name="utility:down" size="x-small"></lightning-icon>
                  </span>
                </div>
                <div id="id-type-listbox" 
                     class="slds-dropdown slds-dropdown_length-5 slds-dropdown_fluid" 
                     role="listbox"
                     style={idTypeDropdownStyle}>
                  <ul class="slds-listbox slds-listbox_vertical" role="presentation">
                    <template for:each={groupedIdTypeOptions} for:item="group">
                      <li key={group.label} role="presentation" class="slds-listbox__item">
                        <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small slds-has-divider_bottom-space" 
                             role="presentation">
                          <h3 class="slds-listbox__option-header" title={group.label}>{group.label}</h3>
                        </div>
                      </li>
                      <template for:each={group.options} for:item="option">
                        <li key={option.value} role="presentation" class="slds-listbox__item">
                          <div class="slds-media slds-listbox__option slds-listbox__option_plain slds-media_small" 
                               role="option"
                               data-value={option.value}
                               data-label={option.label}
                               onclick={handleIdTypeOptionClick}>
                            <span class="slds-media__body">
                              <span class="slds-truncate" title={option.label}>{option.label}</span>
                            </span>
                          </div>
                        </li>
                      </template>
                    </template>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <template if:true={showIdNumber}>
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              label={idNumberLabel}
              name="ID_Number"
              value={ID_Number}
              onchange={handleInput}
              required>
            </lightning-input>
          </div>
        </template>
        
        <template if:true={showIdState}>
          <div class="addressAndIdInfo-input-field">
            <lightning-combobox
              name="ID_State"
              label="ID State"
              value={ID_State}
              options={stateOptions}
              onchange={handleInput}
              required>
            </lightning-combobox>
          </div>
        </template>
        
        <template if:true={showIdCountry}>
          <div class="addressAndIdInfo-input-field">
            <lightning-combobox
              name="ID_Country"
              label="ID Country"
              value={ID_Country}
              options={countryOptions}
              onchange={handleInput}
              required>
            </lightning-combobox>
          </div>
        </template>
      </div>
      
      <div class="addressAndIdInfo-input-row">
        <template if:true={showIdIssueDate}>
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              name="ID_Issued_Date"
              label="ID Issue Date (mm/dd/yyyy)"
              value={ID_Issued_Date}
              onchange={handleIDIssueDateInput}
              maxlength="10"
              placeholder="mm/dd/yyyy"
              required>
            </lightning-input>
            <div if:true={idIssueDateError} class="error-message">{idIssueDateError}</div>
          </div>
        </template>
        
        <template if:true={showIdExpirationDate}>
          <div class="addressAndIdInfo-input-field">
            <lightning-input
              type="text"
              name="ID_Expiration_Date"
              label="ID Expiration Date (mm/dd/yyyy)"
              value={ID_Expiration_Date}
              onchange={handleIDExpirationDateInput}
              maxlength="10"
              placeholder="mm/dd/yyyy"
              required>
            </lightning-input>
            <div if:true={idExpirationDateError} class="error-message">{idExpirationDateError}</div>
          </div>
        </template>
      </div>
      <div class="addressAndIdInfo-button-row">
        <lightning-button 
            label="Add Applicant"
            class="addressAndIdInfo-add-applicant-button"
            onclick={handleAddApplicant}
            variant="brand">
        </lightning-button> 
        <div class="addressAndIdInfo-right-buttons">
          <lightning-button 
            label="Back" 
            onclick={handleBack} 
            variant="neutral">
          </lightning-button> 
          <lightning-button 
            label="Save & Continue" 
            onclick={handleNext} 
            variant="brand" 
            disabled={isSaveDisabled}>
          </lightning-button>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Address Confirmation Modal -->
  <template if:true={showAddressConfirmationModal}>
    <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
      <div class="slds-modal__container">
        <!-- Modal Header -->
        <header class="slds-modal__header">
          <button class="slds-button slds-modal__close slds-button_icon" title="Close" onclick={handleCancelAddApplicant}>
            <lightning-icon icon-name="utility:close" alternative-text="Close" size="small"></lightning-icon>
            <span class="slds-assistive-text">Close</span>
          </button>
          <h2 class="slds-modal__title slds-hyphenate">Before you continue...</h2>
        </header>
  
        <!-- Modal Body -->
        <div class="slds-modal__content slds-p-around_medium">
          <p>Please verify your address:</p>
          <div class="slds-box slds-theme_default slds-m-vertical_small">
            <p>{Physical_Street_Address}</p>
            <p>{Physical_City}, {Physical_State} {Physical_Zip_code}</p>
          </div>
          <p>If this address is correct, click "Continue."</p>
        </div>
  
        <!-- Modal Footer -->
        <footer class="slds-modal__footer">
          <lightning-button variant="brand" label="Continue" onclick={handleContinueAddApplicant}></lightning-button>
          <lightning-button variant="neutral" label="Cancel" onclick={handleCancelAddApplicant} class="slds-m-left_small"></lightning-button>
        </footer>
      </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
  </template>
</template>












<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 05-16-2025
  @last modified by  : <EMAIL>
-->
<template>
    <div class="NewApp-container slds-p-around_medium">
      <h2 class="slds-text-heading_medium">Let's get started.</h2>
      
      <div class="slds-grid slds-gutters">
        <div class="slds-col">
          <lightning-combobox
            label="Are you a current Redwood Credit Union Member?"
            name="membershipStatus"
            value={membershipStatus}
            options={membershipStatusOptions}
            onchange={handleMembershipChange}
            class="membership-status-dropdown"
          ></lightning-combobox>
        </div>

        <div class="slds-col">
          <template if:true={isExistingMember}>
            <lightning-combobox
              label="Select your existing account type"
              name="accountType"
              value={selectedAccountType}
              options={accountTypeOptions}
              onchange={handleAccountTypeChange}
              class="account-type-dropdown"
            ></lightning-combobox>
          </template>
        </div>
      </div>

      <template if:true={isNewMember}>
        <div class="NewApp-info-box slds-m-top_medium">
          <p class="slds-text-body_regular"><strong>To complete this application, you'll need:</strong></p>
          <lightning-layout-item padding="around-small">
            <ul class="slds-list_dotted">
              <li>Your Social Security Number or ITIN.</li>
              <li>A date of birth for all account holders.</li>
              <li>A phone number.</li>
              <li>An email address.</li>
              <li>A physical U.S. address.</li>
              <li>A debit card or account information for funding new account(s).</li>
              <li>
                A valid ID (Driver's License, Matricula Consular ID, Military ID, Passport, or Permanent
                Resident Card).
              </li>
            </ul>
          </lightning-layout-item>
        </div>
      </template>

      <div class="NewApp-buttons slds-m-top_large">
        <lightning-button 
          label="Next" 
          variant="brand" 
          disabled={isNextDisabled} 
          onclick={handleNext}
          class="NewApp-next"
        ></lightning-button>
        <lightning-button 
          label="Go Back" 
          variant="neutral" 
          onclick={handleBack}
          class="NewApp-go-back slds-m-left_x-small"
        ></lightning-button>
      </div>
    </div>
  
</template>





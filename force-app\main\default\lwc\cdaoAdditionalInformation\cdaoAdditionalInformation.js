import { LightningElement, track, api } from 'lwc';
import { loadStyle } from 'lightning/platformResourceLoader';
import sharedStyles from '@salesforce/resourceUrl/sharedStyles';
import { LABELS, OPTIONS } from './cdaoAdditionalInformationConstants';
export default class CdaoAdditionalInformation extends LightningElement {
    LABELS = LABELS; // Expose to HTML
    OPTIONS = OPTIONS;
    // Public properties passed from parent component
    @api applicationData;
    @api getCardSelectionStatus;
    // Reactive properties for form inputs
    @track Verbal_Password_Value = '';
    @track Verbal_Password_Hint_Value = '';
    @track Housing_Status_Value = '';
    @track College_Student_Value = '';
    /**
     * Loads shared styles and initializes form fields from applicationData.
     * Also determines whether to show extra fields based on card selection status.
     */
    connectedCallback() {
        loadStyle(this, sharedStyles);
        if (this.applicationData) {
            this.Verbal_Password_Value = this.applicationData.Verbal_Password || '';
            this.Verbal_Password_Hint_Value = this.applicationData.Verbal_Password_Hint || '';
            this.Housing_Status_Value = this.applicationData.Housing_Status || '';
            this.College_Student_Value = this.applicationData.College_Student || '';
        }
    }
    get isCCSelected() {
        if (typeof this.getCardSelectionStatus === 'function') {
            return this.getCardSelectionStatus();
        }
        return false; 
    }
    
    /**
     * Computed property to determine if the Save/Next button should be disabled.
     * Returns true if all main input fields are empty (i.e., form is incomplete).
     */
    get isSaveDisabled() {
        return !this.Verbal_Password_Value &&
               !this.Verbal_Password_Hint_Value &&
               !this.Housing_Status_Value &&
               !this.College_Student_Value;
    }

    /**
     * Handler for user input on form fields.
     * Updates corresponding tracked properties based on the label of the input.
     */
    handleInput(event) {
        const label = event.target.label;
        const value = event.detail.value;
        if (label === this.LABELS.VERBAL_PASSWORD) {
            this.Verbal_Password_Value = value;
        } else if (label === this.LABELS.VERBAL_PASSWORD_HINT) {
            this.Verbal_Password_Hint_Value = value;
        } else if (label === this.LABELS.HOUSING_STATUS_LABEL) {
            this.Housing_Status_Value = value;
        }else if (label === this.LABELS.COLLEGE_STUDENT_LABEL) {
            this.College_Student_Value = value;
        }
    }
  
    /**
     * Handles clicking the Next button.
     * Constructs an object with all form data and dispatches a 'next' event
     * with the collected additionalInfo to the parent component.
     */
    handleNext() {
        const additionalInfo = {
            Verbal_Password: this.Verbal_Password_Value,
            Verbal_Password_Hint: this.Verbal_Password_Hint_Value,
            Housing_Status: this.Housing_Status_Value,
            College_Student: this.College_Student_Value
        };

        this.dispatchEvent(new CustomEvent('next', { detail: additionalInfo }));
    }
    /**
     * Handles clicking the Back button.
     * Dispatches a 'back' event to the parent component.
     */
    handleBack() {
        const eventDetails ={
                nextScreen : 'primaryMemberInfo'
        };
        this.dispatchEvent(new CustomEvent('back',{
            detail: eventDetails
        }));
    }
}

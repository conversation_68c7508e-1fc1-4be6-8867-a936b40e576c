import { LightningElement, api } from 'lwc';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';
import { MEMBERSHIP_STATUS_OPTIONS, ACCOUNT_TYPE_OPTIONS, EVENTS } from './cdaoNewAppConstants';

export default class CdaoNewApp extends LightningElement {
    @api applicationData;

    membershipStatus = '';
    selectedAccountType = '';
    @api dateError = '';
    @api dateValue = '';
    
    // Use static options from constants file
    membershipStatusOptions = MEMBERSHIP_STATUS_OPTIONS;
    accountTypeOptions = ACCOUNT_TYPE_OPTIONS;

    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                //logic here
            })
            .catch(error => {
                console.error('Error loading shared styles:', error);
            });

        if (this.applicationData) {
            this.membershipStatus = this.applicationData.membershipStatus || '';
            this.selectedAccountType = this.applicationData.selectedAccountType || '';
            this.dateValue = this.applicationData.dateValue || '';
        }
    }

    get isExistingMember() {
        return this.membershipStatus === 'Existing Member';
    }

    get isNewMember() {
        return this.membershipStatus === 'New Member';
    }

    get isNextDisabled() {
        if (this.membershipStatus === 'New Member') {
            return false;
        }
        if (this.membershipStatus === 'Existing Member') {
            return !this.selectedAccountType;
        }
        return true;
    }

    handleMembershipChange(event) {
        this.membershipStatus = event.detail.value;
        this.selectedAccountType = '';
    }

    handleAccountTypeChange(event) {
        this.selectedAccountType = event.detail.value;
    }

    handleBack() {
        const eventDetails ={
                nextScreen : 'welcomePage'
        };
        this.dispatchEvent(new CustomEvent(EVENTS.BACK,{
            detail: eventDetails
        }));
    }

    handleNext() {
        const eventDetails ={
                nextScreen : 'primaryMemberInfo',
                screenData:  {
                    membershipStatus: this.membershipStatus,
                    selectedAccountType: this.selectedAccountType,
                    dateValue: this.dateValue
                }
        };
        this.dispatchEvent(new CustomEvent(EVENTS.NEXT, {
            detail: eventDetails
        }));
    }

    
}




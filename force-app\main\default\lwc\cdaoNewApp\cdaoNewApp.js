import { LightningElement, api, track, wire } from 'lwc';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';
import { EVENTS } from './cdaoNewAppConstants';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import { getPicklistValues } from 'lightning/uiObjectInfoApi';
import APPLICATION_OBJECT from '@salesforce/schema/DAO_Application__c';
import MEMBERSHIP_STATUS_FIELD from '@salesforce/schema/DAO_Application__c.CDAO_Memebership_Status__c';
import ACCOUNT_TYPE_FIELD from '@salesforce/schema/DAO_Application__c.CDAO_Selected_Account_Type__c';

export default class CdaoNewApp extends LightningElement {
    @api applicationData;
    @track membershipStatus = '';
    @track selectedAccountType = '';

    // Remove these static imports from constants
    // membershipStatusOptions = MEMBERSHIP_STATUS_OPTIONS;
    // accountTypeOptions = ACCOUNT_TYPE_OPTIONS;

    // Replace with these dynamic arrays
    membershipStatusOptions = [];
    accountTypeOptions = [];

    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                //logic here
            })
            .catch(error => {
                console.error('Error loading shared styles:', error);
            });

        if (this.applicationData) {
            this.membershipStatus = this.applicationData.membershipStatus || '';
            this.selectedAccountType = this.applicationData.selectedAccountType || '';
        }
    }

    // Get object info for picklist values
    @wire(getObjectInfo, { objectApiName: APPLICATION_OBJECT })
    objectInfo;

    // Get membership status picklist values
    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: MEMBERSHIP_STATUS_FIELD })
    wiredMembershipStatusOptions({ error, data }) {
        if (data) {
            this.membershipStatusOptions = data.values;
        } else if (error) {
            console.error('Error loading membership status options:', error);
        }
    }

    // Get account type picklist values
    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: ACCOUNT_TYPE_FIELD })
    wiredAccountTypeOptions({ error, data }) {
        if (data) {
            this.accountTypeOptions = data.values;
        } else if (error) {
         console.error('Error loading account type options:', error);
        }
    }

    get isExistingMember() {
        return this.membershipStatus === 'Existing Member';
    }

    get isNewMember() {
        return this.membershipStatus === 'New Member';
    }

    get isNextDisabled() {
       if (!this.membershipStatus || !this.selectedAccountType) {
            return true;
        }
        return false;
    }

    handleMembershipChange(event) {
        this.membershipStatus = event.detail.value;
        this.selectedAccountType = '';
    }

    handleAccountTypeChange(event) {
        this.selectedAccountType = event.detail.value;
    }

    handleBack() {
        const eventDetails ={
                nextScreen : 'welcomePage'
        };
        this.dispatchEvent(new CustomEvent(EVENTS.BACK,{
            detail: eventDetails
        }));
    }

    handleNext() {
        const eventDetails ={
                nextScreen : 'primaryMemberInfo',
                screenData:  {
                    membershipStatus: this.membershipStatus,
                    selectedAccountType: this.selectedAccountType,
                }
        };
        this.dispatchEvent(new CustomEvent(EVENTS.NEXT, {
            detail: eventDetails
        }));
    }

    
}




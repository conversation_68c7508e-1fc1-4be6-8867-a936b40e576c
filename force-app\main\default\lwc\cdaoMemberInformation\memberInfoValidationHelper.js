import { ERROR_MESSAGES, VALIDATION_PATTERNS } from './memberInfoConstants';

/**
 * Helper class for member information validation
 */
const ValidationHelper = {
    // Validate a name field
    validateName(value, isRequired, fieldName) {
        if (!value && isRequired) {
            return ERROR_MESSAGES.REQUIRED_FIELD;
        }
        
        if (value && !VALIDATION_PATTERNS.NAME.test(value)) {
            return ERROR_MESSAGES.INVALID_NAME;
        }
        
        return '';
    },
    
    // Validate email
    validateEmail(value, isRequired) {
        if (!value && isRequired) {
            return ERROR_MESSAGES.REQUIRED_FIELD;
        }
        
        if (value && !VALIDATION_PATTERNS.EMAIL.test(value)) {
            return ERROR_MESSAGES.INVALID_EMAIL;
        }
        
        return '';
    },
    
    // Validate phone
    validatePhone(value, isRequired) {
        if (!value && isRequired) {
            return ERROR_MESSAGES.REQUIRED_FIELD;
        }
        
        if (value && !VALIDATION_PATTERNS.PHONE.test(value)) {
            return ERROR_MESSAGES.INVALID_PHONE;
        }
        
        return '';
    },
    
    // Validate SSN
    validateSSN(value, isRequired) {
        if (!value && isRequired) {
            return ERROR_MESSAGES.REQUIRED_FIELD;
        }
        
        if (value && !VALIDATION_PATTERNS.SSN.test(value)) {
            return ERROR_MESSAGES.INVALID_SSN;
        }
        
        return '';
    },
    
    // Validate date of birth
    validateDOB(value, isRequired) {
        if (!value && isRequired) {
            return ERROR_MESSAGES.REQUIRED_FIELD;
        }
        
        if (value) {
            const dobDate = new Date(value);
            const today = new Date();
            
            // Check if date is valid
            if (isNaN(dobDate.getTime())) {
                return ERROR_MESSAGES.INVALID_DOB;
            }
            
            // Check if date is in the future
            if (dobDate > today) {
                return ERROR_MESSAGES.FUTURE_DATE;
            }
            
            // Check if person is at least 18 years old
            const age = today.getFullYear() - dobDate.getFullYear();
            const monthDiff = today.getMonth() - dobDate.getMonth();
            const dayDiff = today.getDate() - dobDate.getDate();
            
            if (age < 18 || (age === 18 && (monthDiff < 0 || (monthDiff === 0 && dayDiff < 0)))) {
                return ERROR_MESSAGES.MIN_AGE;
            }
        }
        
        return '';
    },
    
    formatPhone(value) {
        const input = value.replace(/\D/g, '');
        let formattedPhone = '';
        
        if (input.length > 0) {
            if (input.length <= 3) {
                formattedPhone = input;
            } else if (input.length <= 6) {
                formattedPhone = input.slice(0, 3) + '-' + input.slice(3);
            } else {
                formattedPhone = input.slice(0, 3) + '-' + input.slice(3, 6) + '-' + input.slice(6, 10);
            }
        }
        
        return formattedPhone;
    },
    
    formatDate(value) {
        const input = value.replace(/\D/g, '');
        let formattedDate = '';
        
        if (input.length > 0) {
            if (input.length <= 2) {
                formattedDate = input;
                if (input.length === 2) {
                    formattedDate += '/';
                }
            } else if (input.length <= 4) {
                formattedDate = input.slice(0, 2) + '/' + input.slice(2);
                if (input.length === 4) {
                    formattedDate += '/';
                }
            } else {
                formattedDate = input.slice(0, 2) + '/' + input.slice(2, 4) + '/' + input.slice(4, 8);
            }
        }
        
        return formattedDate;
    },

    // New methods moved from component
    validateField(field, validationFn, args = []) {
        // Skip validation if no validation function defined
        if (!validationFn) {
            return true;
        }
        
        // Call the validation function with arguments
        const result = validationFn(field.value, ...args);
        
        // Update error message
        field.error = result.errorMessage;
        
        return result.isValid;
    },

    validateAllFields(fields) {
        const results = [];
        Object.keys(fields).forEach(fieldName => {
            const field = fields[fieldName];
            if (field.validationFn) {
                const validationFn = this[field.validationFn];
                const args = field.validationArgs || [];
                const result = this.validateField(field, validationFn, args);
                results.push(result);
            } else {
                results.push(true);
            }
        });
        return results;
    },

    isFormValid(fields, validateAll = false) {
        // Only validate all fields if explicitly requested
        if (validateAll) {
            this.validateAllFields(fields);
        }
        
        // Check if required fields have values and no errors
        return Object.keys(fields).every(fieldName => {
            const field = fields[fieldName];
            return (!field.required || field.value) && !field.error;
        });
    },

    handleInput(event, fields, component) {
        const fieldName = event.target.name;
        let value = event.target.value;
        
        // Handle special field types
        if (fieldName === 'First_Name' || fieldName === 'Middle_Name' || fieldName === 'Last_Name') {
            // Only allow alphabetic characters and spaces
            value = value.replace(/[^a-zA-Z\s]/g, '');
        } else if (fieldName === 'Phone') {
            value = this.formatPhone(value);
            
            // Update input field
            const phoneInput = component.template.querySelector(`lightning-input[name="${fieldName}"]`);
            if (phoneInput) {
                phoneInput.value = value;
            }
        } else if (fieldName === 'SSN_TIN') {
            value = value.replace(/\D/g, '');
            
            // Update input field if not focused
            const ssnInput = component.template.querySelector('lightning-input[data-id="ssn-input"]');
            if (ssnInput && !component.isSSNFocused) {
                ssnInput.value = value.replace(/./g, '•');
            }
        }
        
        // Update field value
        if (fields[fieldName]) {
            fields[fieldName].value = value;
            
            // Validate the field
            if (fields[fieldName].validationFn) {
                const validationFn = this[fields[fieldName].validationFn];
                const args = fields[fieldName].validationArgs || [];
                this.validateField(fields[fieldName], validationFn, args);
            }
        }
    },

    handleDOBInput(event, fields, component) {
        const value = event.target.value;
        const formattedDate = this.formatDate(value);
        
        // Update input field
        const dobInput = component.template.querySelector('lightning-input[name="Date_of_Birth"]');
        if (dobInput) {
            dobInput.value = formattedDate;
        }
        
        // Update field value
        fields.Date_of_Birth.value = formattedDate;
        
        // Only validate if user has entered something
        if (formattedDate.length > 0) {
            if (formattedDate.length === 10) {
                // Full date entered, perform complete validation
                if (fields.Date_of_Birth.validationFn) {
                    const validationFn = this[fields.Date_of_Birth.validationFn];
                    this.validateField(fields.Date_of_Birth, validationFn);
                }
            } else {
                // Partial date, show format message
                fields.Date_of_Birth.error = 'Please complete the date in format: mm/dd/yyyy';
            }
        } else {
            // Clear error if field is empty (will be caught by required validation later)
            fields.Date_of_Birth.error = '';
        }
    }
};

export default ValidationHelper;





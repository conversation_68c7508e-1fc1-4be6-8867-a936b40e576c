import { ERROR_MESSAGES, ID_TYPE_GROUPS, ID_TYPE_FIELD_VISIBILITY, DEFAULT_FIELD_VISIBILITY } from './addressIdConstants';

/**
 * Helper class for address and ID information validation and data handling
 */
const AddressIdHelper = {
    /**
     * Validates a ZIP code
     * @param {string} zipCode - The ZIP code to validate
     * @returns {boolean} - Whether the ZIP code is valid
     */
    validateZipCode(zipCode) {
        const zipRegex = /^\d{5}$/;
        if (!zipCode) {
            return false;
        } else if (!zipRegex.test(zipCode)) {
            return false;
        }
        return true;
    },

    /**
     * Formats a date string to mm/dd/yyyy format
     * @param {string} dateStr - The date string to format
     * @returns {string} - The formatted date string
     */
    formatDate(dateStr) {
        // Remove any non-digit characters
        let digitsOnly = dateStr.replace(/\D/g, '');
        
        // Format as mm/dd/yyyy
        if (digitsOnly.length > 0) {
            // Add first slash after month (after 2 digits)
            if (digitsOnly.length > 2) {
                digitsOnly = digitsOnly.substring(0, 2) + '/' + digitsOnly.substring(2);
            }
            
            // Add second slash after day (after 5 characters including first slash)
            if (digitsOnly.length > 5) {
                digitsOnly = digitsOnly.substring(0, 5) + '/' + digitsOnly.substring(5, 9);
            }
        }
        
        return digitsOnly;
    },

    /**
     * Validates an ID issue date
     * @param {string} dateStr - The date string to validate
     * @param {Object} component - The component instance for error handling
     * @returns {boolean} - Whether the date is valid
     */
    validateIDIssueDate(dateStr, component) {
        if (!dateStr) {
            component.idIssueDateError = ERROR_MESSAGES.REQUIRED_DATE;
            return false;
        }
        
        // Check date format
        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
        if (!dateRegex.test(dateStr)) {
            component.idIssueDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
            return false;
        }
        
        // Parse date
        const parts = dateStr.split('/');
        const month = parseInt(parts[0], 10);
        const day = parseInt(parts[1], 10);
        const year = parseInt(parts[2], 10);
        
        // Check if date is valid
        const date = new Date(year, month - 1, day);
        if (
            date.getFullYear() !== year ||
            date.getMonth() !== month - 1 ||
            date.getDate() !== day
        ) {
            component.idIssueDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
            return false;
        }
        
        // Check if date is in the future
        const today = new Date();
        if (date > today) {
            component.idIssueDateError = ERROR_MESSAGES.FUTURE_DATE;
            return false;
        }
        
        // Date is valid
        component.idIssueDateError = '';
        return true;
    },

    /**
     * Validates an ID expiration date
     * @param {string} expirationDateStr - The expiration date string to validate
     * @param {string} issueDateStr - The issue date string for comparison
     * @param {Object} component - The component instance for error handling
     * @returns {boolean} - Whether the date is valid
     */
    validateIDExpirationDate(expirationDateStr, issueDateStr, component) {
        if (!expirationDateStr) {
            component.idExpirationDateError = ERROR_MESSAGES.REQUIRED_DATE;
            return false;
        }
        
        // Check date format
        const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
        if (!dateRegex.test(expirationDateStr)) {
            component.idExpirationDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
            return false;
        }
        
        // Parse expiration date
        const expParts = expirationDateStr.split('/');
        const expMonth = parseInt(expParts[0], 10);
        const expDay = parseInt(expParts[1], 10);
        const expYear = parseInt(expParts[2], 10);
        
        // Check if date is valid
        const expDate = new Date(expYear, expMonth - 1, expDay);
        if (
            expDate.getFullYear() !== expYear ||
            expDate.getMonth() !== expMonth - 1 ||
            expDate.getDate() !== expDay
        ) {
            component.idExpirationDateError = ERROR_MESSAGES.INVALID_DATE_FORMAT;
            return false;
        }
        
        // Check if expiration date is in the past
        const today = new Date();
        if (expDate < today) {
            component.idExpirationDateError = ERROR_MESSAGES.PAST_DATE;
            return false;
        }
        
        // If issue date is provided, check that expiration date is after issue date
        if (issueDateStr && dateRegex.test(issueDateStr)) {
            const issueParts = issueDateStr.split('/');
            const issueMonth = parseInt(issueParts[0], 10);
            const issueDay = parseInt(issueParts[1], 10);
            const issueYear = parseInt(issueParts[2], 10);
            
            const issueDate = new Date(issueYear, issueMonth - 1, issueDay);
            
            if (expDate < issueDate) {
                component.idExpirationDateError = ERROR_MESSAGES.EXPIRATION_BEFORE_ISSUE;
                return false;
            }
        }
        
        // Date is valid
        component.idExpirationDateError = '';
        return true;
    },

    /**
     * Validates all required fields
     * @param {Object} component - The component instance
     * @returns {boolean} - Whether all fields are valid
     */
    validateAllFields(component) {
        // Clear previous errors
        component.zipCodeError = '';
        component.mailingZipCodeError = '';
        
        // Validate dates if they are required for the selected ID type
        let issueDateValid = true;
        let expirationDateValid = true;
        
        if (component.showIdIssueDate && component._applicationData?.IdentityInfo?.ID_Issued_Date) {
            issueDateValid = this.validateIDIssueDate(
                component._applicationData.IdentityInfo.ID_Issued_Date, 
                component
            );
        }
        
        if (component.showIdExpirationDate && component._applicationData?.IdentityInfo?.ID_Expiration_Date) {
            expirationDateValid = this.validateIDExpirationDate(
                component._applicationData.IdentityInfo.ID_Expiration_Date,
                component._applicationData.IdentityInfo.ID_Issued_Date,
                component
            );
        }
        
        if (!issueDateValid || !expirationDateValid) {
            return false;
        }
        
        // Basic validation for required fields
        let baseFieldsValid = component.Physical_Street_Address && 
               component.Physical_City && 
               component.Physical_State && 
               component.Physical_Zip_code && 
               this.validateZipCode(component.Physical_Zip_code) &&
               component.Type &&
               component.ID_Number;
        
        // Add conditional validation based on ID type
        if (component.showIdState) {
            baseFieldsValid = baseFieldsValid && component.ID_State;
        }
        
        if (component.showIdCountry) {
            baseFieldsValid = baseFieldsValid && component.ID_Country;
        }
        
        if (component.showIdIssueDate) {
            baseFieldsValid = baseFieldsValid && component.ID_Issued_Date;
        }
        
        if (component.showIdExpirationDate) {
            baseFieldsValid = baseFieldsValid && component.ID_Expiration_Date;
        }
        
        if (!baseFieldsValid) {
            // Set ZIP code error if needed
            if (component.Physical_Zip_code && !this.validateZipCode(component.Physical_Zip_code)) {
                component.zipCodeError = ERROR_MESSAGES.INVALID_ZIP;
            }
            return false;
        }
        
        // If using different mailing address, validate those fields too
        if (!component.Mailing_Address_same_as_Physical) {
            const mailingValid = component.Mailing_Street_Address && 
                   component.Mailing_City && 
                   component.Mailing_State && 
                   component.Mailing_Zip_code &&
                   this.validateZipCode(component.Mailing_Zip_code);
           
            if (!mailingValid) {
                // Set mailing ZIP code error if needed
                if (component.Mailing_Zip_code && !this.validateZipCode(component.Mailing_Zip_code)) {
                    component.mailingZipCodeError = ERROR_MESSAGES.INVALID_ZIP;
                }
                return false;
            }
        }
        
        return true;
    },

    /**
     * Shows validation errors on form elements
     * @param {Object} template - The component's template
     */
    showValidationErrors(template) {
        template.querySelectorAll('lightning-input, lightning-combobox').forEach(element => {
            if (element.reportValidity) {
                element.reportValidity();
            }
        });
    },

    /**
     * Determines which ID fields to display based on ID type
     * @param {string} idType - The selected ID type
     * @returns {Object} - Object containing field visibility flags and labels
     */
    determineIdFieldVisibility(idType) {
        // Get field visibility rules for the selected ID type, or use defaults
        return ID_TYPE_FIELD_VISIBILITY[idType] || DEFAULT_FIELD_VISIBILITY;
    },

    /**
     * Gets a flat list of ID type options from grouped options
     * @param {Array} groupedOptions - The grouped ID type options
     * @returns {Array} - Flat list of ID type options
     */
    getFlatIdTypeOptions(groupedOptions) {
        return groupedOptions.reduce((options, group) => {
            return options.concat(group.options);
        }, []);
    },

    /**
     * Gets the label for an ID type value
     * @param {string} idType - The ID type value
     * @param {Array} groupedOptions - The grouped ID type options
     * @returns {string} - The label for the selected ID type
     */
    getIdTypeLabel(idType, groupedOptions) {
        if (!idType || !groupedOptions) {
            return '';
        }
        
        for (const group of groupedOptions) {
            for (const option of group.options) {
                if (option.value === idType) {
                    return option.label;
                }
            }
        }
        
        return '';
    },

    /**
     * Creates a flat list of options from grouped options
     * @param {Array} groupedOptions - The grouped options array
     * @returns {Array} - A flat list of options
     */
    getFlatIdTypeOptions(groupedOptions) {
        const flatOptions = [];
        if (groupedOptions) {
            groupedOptions.forEach(group => {
                group.options.forEach(option => {
                    flatOptions.push(option);
                });
            });
        }
        return flatOptions;
    }
};

export default AddressIdHelper;





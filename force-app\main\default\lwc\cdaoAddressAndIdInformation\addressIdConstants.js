// Field definitions
export const FIELD_DEFINITIONS = {
    // Physical Address Fields
    Physical_Street_Address: {
        required: true,
        label: 'Address',
        section: 'PhysicalAddress'
    },
    Physical_City: {
        required: true,
        label: 'City',
        section: 'PhysicalAddress'
    },
    Physical_State: {
        required: true,
        label: 'State',
        section: 'PhysicalAddress',
        type: 'picklist'
    },
    Physical_Zip_code: {
        required: true,
        label: 'ZIP Code',
        section: 'PhysicalAddress',
        pattern: '[0-9]{5}',
        maxLength: 5,
        validationFn: 'validateZipCode'
    },
    
    // Mailing Address Fields
    Mailing_Street_Address: {
        required: true,
        label: 'Mailing Address',
        section: 'MailingAddress'
    },
    Mailing_City: {
        required: true,
        label: 'Mailing City',
        section: 'MailingAddress'
    },
    Mailing_State: {
        required: true,
        label: 'Mailing State',
        section: 'MailingAddress',
        type: 'picklist'
    },
    Mailing_Zip_code: {
        required: true,
        label: 'Mailing ZIP Code',
        section: 'MailingAddress',
        pattern: '[0-9]{5}',
        maxLength: 5,
        validationFn: 'validateZipCode'
    },
    
    // ID Fields
    Type: {
        required: true,
        label: 'ID Type',
        section: 'IdentityInfo'
    },
    ID_Number: {
        required: true,
        label: 'ID Number',
        section: 'IdentityInfo'
    },
    ID_State: {
        required: false,
        label: 'ID State',
        section: 'IdentityInfo',
        type: 'picklist'
    },
    ID_Country: {
        required: false,
        label: 'ID Country',
        section: 'IdentityInfo',
        type: 'picklist',
        defaultValue: 'US'
    },
    ID_Issued_Date: {
        required: false,
        label: 'ID Issue Date (mm/dd/yyyy)',
        section: 'IdentityInfo',
        validationFn: 'validateIDIssueDate'
    },
    ID_Expiration_Date: {
        required: false,
        label: 'ID Expiration Date (mm/dd/yyyy)',
        section: 'IdentityInfo',
        validationFn: 'validateIDExpirationDate'
    }
};

// ID Type Groupings
export const ID_TYPE_GROUPS = [
    {
        label: 'Standard IDs',
        options: [
            { label: 'Driver\'s License', value: 'Driver\'s License' },
            { label: 'State ID', value: 'State ID' },
            { label: 'Passport/Passport Card', value: 'Passport/Passport Card' }
        ]
    },
    {
        label: 'Consular IDs',
        options: [
            { label: 'Matricula Consular ID (Mexico)', value: 'Matricula Consular ID (Mexico)' },
            { label: 'Guatemalan Consular ID', value: 'Guatemalan Consular ID' }
        ]
    },
    {
        label: 'US Government IDs',
        options: [
            { label: 'Military ID', value: 'Military ID' },
            { label: 'Permanent Resident Card/Resident Alien Card', value: 'Permanent Resident Card' }
        ]
    },
    {
        label: 'Other IDs',
        options: [
            { label: 'SF City ID Card', value: 'SF City ID Card' },
            { label: 'Tribal ID', value: 'Tribal ID' }
        ]
    }
];

// ID Type Field Visibility Rules
export const ID_TYPE_FIELD_VISIBILITY = {
    'Driver\'s License': {
        showIdNumber: true,
        showIdState: true,
        showIdCountry: false,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'Driver\'s License Number'
    },
    'State ID': {
        showIdNumber: true,
        showIdState: true,
        showIdCountry: false,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'ID Card Number'
    },
    'Passport/Passport Card': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: true,
        showIdIssueDate: false,
        showIdExpirationDate: true,
        idNumberLabel: 'Passport Number'
    },
    'Matricula Consular ID (Mexico)': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: true,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'ID Card Number'
    },
    'Guatemalan Consular ID': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: true,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'ID Card Number'
    },
    'Military ID': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: false,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'Military ID Number'
    },
    'Permanent Resident Card': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: false,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'Alien Registration Number'
    },
    'SF City ID Card': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: false,
        showIdIssueDate: true,
        showIdExpirationDate: true,
        idNumberLabel: 'ID Card Number'
    },
    'Tribal ID': {
        showIdNumber: true,
        showIdState: false,
        showIdCountry: false,
        showIdIssueDate: false,
        showIdExpirationDate: false,
        idNumberLabel: 'Tribal ID Number'
    }
};

// Default field visibility
export const DEFAULT_FIELD_VISIBILITY = {
    showIdNumber: true,
    showIdState: false,
    showIdCountry: false,
    showIdIssueDate: false,
    showIdExpirationDate: false,
    idNumberLabel: 'ID Number'
};

// Error messages
export const ERROR_MESSAGES = {
    REQUIRED_FIELD: 'This field is required.',
    INVALID_ZIP: 'Please enter a valid 5-digit ZIP code.',
    INVALID_DATE_FORMAT: 'Please complete the date in format: mm/dd/yyyy',
    REQUIRED_DATE: 'This date is required.',
    FUTURE_DATE: 'Date cannot be in the future.',
    PAST_DATE: 'Expiration date must be in the future.',
    EXPIRATION_BEFORE_ISSUE: 'Expiration date cannot be before issue date.',
    ID_ISSUE_DATE_REQUIRED: 'ID issue date is required',
    ID_EXPIRATION_DATE_REQUIRED: 'ID expiration date is required'
};


// Event names
export const EVENTS = {
    NEXT: 'next',
    BACK: 'back',
    ADD_APPLICANT: 'addapplicant'
};

// Default application data structure
export const DEFAULT_APPLICATION_DATA = {
    PhysicalAddress: {},
    MailingAddress: {},
    IdentityInfo: {},
    Mailing_Address_same_as_Physical: true
};

<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Time_in_Services_Months__c</fullName>
    <label>Time in Services Months</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>January</fullName>
                <default>false</default>
                <label>January</label>
            </value>
            <value>
                <fullName>February</fullName>
                <default>false</default>
                <label>February</label>
            </value>
            <value>
                <fullName>March</fullName>
                <default>false</default>
                <label>March</label>
            </value>
            <value>
                <fullName>April</fullName>
                <default>false</default>
                <label>April</label>
            </value>
            <value>
                <fullName>May</fullName>
                <default>false</default>
                <label>May</label>
            </value>
            <value>
                <fullName>June</fullName>
                <default>false</default>
                <label>June</label>
            </value>
            <value>
                <fullName>July</fullName>
                <default>false</default>
                <label>July</label>
            </value>
            <value>
                <fullName>August</fullName>
                <default>false</default>
                <label>August</label>
            </value>
            <value>
                <fullName>September</fullName>
                <default>false</default>
                <label>September</label>
            </value>
            <value>
                <fullName>October</fullName>
                <default>false</default>
                <label>October</label>
            </value>
            <value>
                <fullName>November</fullName>
                <default>false</default>
                <label>November</label>
            </value>
            <value>
                <fullName>December</fullName>
                <default>false</default>
                <label>December</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>

<template>
    <lightning-card title={cardTitle} icon-name="standard:address">
        <div class="slds-p-horizontal_medium">
            <div class="address-component">
                <!-- Street Address with Auto-complete -->
                <div class="address-field">
                    <lightning-input 
                        type="text" 
                        label={streetLabel} 
                        name="street" 
                        value={street}
                        required={required}
                        onchange={handleInputChange}
                        onkeyup={handleKeyUp}>
                    </lightning-input>
                    
                    <!-- Loading Spinner -->
                    <div if:true={isSearching} class="search-spinner">
                        <lightning-spinner alternative-text="Loading" size="x-small"></lightning-spinner>
                    </div>
                    
                    <!-- Address Suggestions Dropdown -->
                    <div if:true={showSuggestions} class="address-suggestions-dropdown">
                        <ul class="custom-suggestions-list">
                            <template for:each={addressSuggestions} for:item="suggestion">
                                <li key={suggestion.id} 
                                    class="custom-suggestion-item" 
                                    data-id={suggestion.id}
                                    onclick={handleAddressSelection}>
                                    {suggestion.value}
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
                
                <div class="slds-grid slds-gutters">
                    <div class="slds-col">
                        <!-- City -->
                        <div class="address-field">
                            <lightning-input 
                                type="text" 
                                label={cityLabel} 
                                name="city" 
                                value={city}
                                required={required}
                                onchange={handleInputChange}>
                            </lightning-input>
                        </div>
                    </div>
                    <div class="slds-col">
                        <!-- State -->
                        <div class="address-field">
                            <lightning-combobox
                                label={stateLabel}
                                name="state"
                                value={state}
                                options={stateOptions}
                                required={required}
                                onchange={handleInputChange}>
                            </lightning-combobox>
                        </div>
                    </div>
                    <div class="slds-col">
                        <!-- ZIP Code -->
                        <div class="address-field">
                            <lightning-input 
                                type="text" 
                                label={zipLabel} 
                                name="zipCode" 
                                value={zipCode}
                                required={required}
                                onchange={handleInputChange}
                                message-when-value-missing="Please enter a ZIP code"
                                message-when-bad-input={zipCodeError}>
                            </lightning-input>
                            <div if:true={zipCodeError} class="slds-text-color_error slds-text-body_small">
                                {zipCodeError}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </lightning-card>
</template>


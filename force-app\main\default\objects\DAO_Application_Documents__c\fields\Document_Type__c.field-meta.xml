<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Document_Type__c</fullName>
    <label>Document Type</label>
    <required>false</required>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Unexpired Fictitious Name Statement</fullName>
                <default>false</default>
                <label>Unexpired Fictitious Name Statement</label>
            </value>
            <value>
                <fullName>Certificate of Limited Partnership</fullName>
                <default>false</default>
                <label>Certificate of Limited Partnership</label>
            </value>
            <value>
                <fullName>Partnership Agreement</fullName>
                <default>false</default>
                <label>Partnership Agreement</label>
            </value>
            <value>
                <fullName>Government Issued Identification</fullName>
                <default>false</default>
                <label>Government Issued Identification</label>
            </value>
            <value>
                <fullName>Articles of Organization</fullName>
                <default>false</default>
                <label>Articles of Organization</label>
            </value>
            <value>
                <fullName>Articles of Incorporation</fullName>
                <default>false</default>
                <label>Articles of Incorporation</label>
            </value>
            <value>
                <fullName>Corporate Resolution</fullName>
                <default>false</default>
                <label>Corporate Resolution</label>
            </value>
            <value>
                <fullName>Board Resolution, Statement of Officers, Meeting Minutes</fullName>
                <default>false</default>
                <label>Board Resolution, Statement of Officers, Meeting Minutes</label>
            </value>
            <value>
                <fullName>501(c)(3) Identification Letter</fullName>
                <default>false</default>
                <label>501(c)(3) Identification Letter</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
